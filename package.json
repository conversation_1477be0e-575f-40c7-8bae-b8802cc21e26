{"name": "detective-mystery", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "worker:dev": "wrangler dev", "worker:deploy": "wrangler deploy"}, "dependencies": {"@cloudflare/kv-asset-handler": "^0.4.0", "i18next": "^23.10.1", "i18next-browser-languagedetector": "^7.2.0", "lucide-react": "^0.344.0", "openai": "^4.28.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^14.1.0", "uuid": "^9.0.1"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250531.0", "@eslint/js": "^9.9.1", "@types/node": "^22.15.29", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/uuid": "^9.0.8", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.19", "wrangler": "^4.18.0"}}