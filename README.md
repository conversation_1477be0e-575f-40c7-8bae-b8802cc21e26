# Detective Mystery - Cloudflare Worker 部署

这是一个侦探推理游戏，使用 React + TypeScript 构建，部署在 Cloudflare Workers 上。

## 项目结构

```
detective-play/
├── src/
│   ├── worker.ts          # Cloudflare Worker 入口文件
│   ├── App.tsx           # React 应用主组件
│   ├── components/       # React 组件
│   ├── contexts/         # React Context
│   ├── services/         # API 服务
│   └── ...
├── dist/                 # 构建输出目录（静态资源）
├── wrangler.toml         # Cloudflare Worker 配置
├── vite.config.ts        # Vite 构建配置
└── package.json
```

## 开发环境

### 本地开发（前端）
```bash
npm run dev
```
在 `http://localhost:3000` 访问应用

### 本地开发（Worker）
```bash
npm run worker:dev
```
在 `http://localhost:8787` 访问 Worker 环境

## 部署

### 构建项目
```bash
npm run build
```

### 部署到 Cloudflare Workers
```bash
npm run worker:deploy
```

## 配置说明

### wrangler.toml
- `name`: Worker 名称
- `main`: Worker 入口文件
- `assets`: 静态资源配置，指向 `dist` 目录

### Worker 功能
- 服务静态资源（HTML、CSS、JS）
- SPA 路由支持（所有路由都返回 index.html）
- 安全头设置
- 缓存控制

## 环境变量

如需配置 OpenAI API，请在 Cloudflare Dashboard 中设置环境变量：
- `OPENAI_API_KEY`: OpenAI API 密钥

## 命令说明

- `npm run dev`: 启动 Vite 开发服务器
- `npm run build`: 构建 React 应用
- `npm run worker:dev`: 启动 Worker 开发环境
- `npm run worker:deploy`: 部署到 Cloudflare Workers
- `npm run lint`: 代码检查

## 技术栈

- **前端**: React 18 + TypeScript + Vite
- **样式**: Tailwind CSS
- **国际化**: i18next
- **图标**: Lucide React
- **部署**: Cloudflare Workers
- **AI**: OpenAI API
