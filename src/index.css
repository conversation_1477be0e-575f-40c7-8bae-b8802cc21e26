@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Source+Sans+3:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    font-family: 'Source Sans 3', sans-serif;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
  }
}

.font-serif {
  font-family: 'Playfair Display', serif;
}

/* Animated transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Paper texture effect for backgrounds */
.bg-paper {
  background-color: #f8f5e6;
  background-image: url('https://www.transparenttextures.com/patterns/notebook.png');
}
