import { Case } from '../types';

type ChatMessage = {
  role: 'system' | 'user' | 'assistant';
  content: string;
};

export async function generateAIResponse(
  message: string,
  currentCase: Case,
  previousMessages: ChatMessage[]
): Promise<string> {
  try {
    const systemPrompt = `You are a detective's assistant AI helping solve the following case:
Title: ${currentCase.title}
Background: ${currentCase.background}
Objective: ${currentCase.objective}

Available clues:
${currentCase.clues.map(clue => `- ${clue}`).join('\n')}

Characters involved:
${currentCase.characters.map(char => `- ${char.name}: ${char.description}`).join('\n')}

Respond in the same language as the user's message. Keep responses concise and focused on helping solve the case. Don't reveal who is guilty directly, but guide the user towards the solution through careful analysis of clues and evidence.`;

    // Call our Worker's API proxy instead of directly calling external AI API
    // Model name will be determined by environment variables on the server side
    const response = await fetch('/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          { role: "system", content: systemPrompt },
          ...previousMessages,
          { role: "user", content: message }
        ],
        temperature: 0.7,
        max_tokens: 200
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('API Error:', errorData);
      throw new Error(`API request failed: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || "I apologize, but I couldn't generate a response at this time.";
  } catch (error) {
    console.error('Error generating AI response:', error);
    return "I apologize, but I'm having trouble connecting to the AI service at the moment.";
  }
}