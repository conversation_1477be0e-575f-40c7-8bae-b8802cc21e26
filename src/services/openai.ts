import { Case } from '../types';

type ChatMessage = {
  role: 'system' | 'user' | 'assistant';
  content: string;
};

export async function generateAIResponse(
  message: string,
  currentCase: Case,
  previousMessages: ChatMessage[],
  onChunk?: (chunk: string) => void
): Promise<string> {
  try {
    const systemPrompt = `You are a detective's assistant AI helping solve the following case:
Title: ${currentCase.title}
Background: ${currentCase.background}
Objective: ${currentCase.objective}

Available clues:
${currentCase.clues.map(clue => `- ${clue}`).join('\n')}

Characters involved:
${currentCase.characters.map(char => `- ${char.name}: ${char.description}`).join('\n')}

Respond in the same language as the user's message. Keep responses concise and focused on helping solve the case. Don't reveal who is guilty directly, but guide the user towards the solution through careful analysis of clues and evidence.`;

    // Call our Worker's API proxy with streaming enabled
    const response = await fetch('/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          { role: "system", content: systemPrompt },
          ...previousMessages,
          { role: "user", content: message }
        ],
        temperature: 0.7,
        max_tokens: 200,
        stream: true
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error:', errorText);
      throw new Error(`API request failed: ${response.status}`);
    }

    // Handle streaming response
    if (!response.body) {
      throw new Error('No response body received');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullResponse = '';

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);

            if (data === '[DONE]') {
              return fullResponse || "I apologize, but I couldn't generate a response at this time.";
            }

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices?.[0]?.delta?.content;

              if (content) {
                fullResponse += content;
                onChunk?.(content);
              }
            } catch (e) {
              // Skip invalid JSON lines
              continue;
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    return fullResponse || "I apologize, but I couldn't generate a response at this time.";
  } catch (error) {
    console.error('Error generating AI response:', error);
    return "I apologize, but I'm having trouble connecting to the AI service at the moment.";
  }
}