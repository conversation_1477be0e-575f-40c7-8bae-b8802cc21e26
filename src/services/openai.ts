import OpenAI from 'openai';
import { Case } from '../types';

const openai = new OpenAI({
  apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  baseURL: 'https://api.novita.ai/v3/openai',
  dangerouslyAllowBrowser: true
});

type ChatMessage = {
  role: 'system' | 'user' | 'assistant';
  content: string;
};

export async function generateAIResponse(
  message: string, 
  currentCase: Case, 
  previousMessages: ChatMessage[]
): Promise<string> {
  try {
    const systemPrompt = `You are a detective's assistant AI helping solve the following case:
Title: ${currentCase.title}
Background: ${currentCase.background}
Objective: ${currentCase.objective}

Available clues:
${currentCase.clues.map(clue => `- ${clue}`).join('\n')}

Characters involved:
${currentCase.characters.map(char => `- ${char.name}: ${char.description}`).join('\n')}

Respond in the same language as the user's message. Keep responses concise and focused on helping solve the case. Don't reveal who is guilty directly, but guide the user towards the solution through careful analysis of clues and evidence.`;

    const response = await openai.chat.completions.create({
      model: "deepseek/deepseek-r1-0528",
      messages: [
        { role: "system", content: systemPrompt },
        ...previousMessages,
        { role: "user", content: message }
      ],
      temperature: 0.7,
      max_tokens: 200
    });

    return response.choices[0]?.message?.content || "I apologize, but I couldn't generate a response at this time.";
  } catch (error) {
    console.error('Error generating AI response:', error);
    return "I apologize, but I'm having trouble connecting to the AI service at the moment.";
  }
}