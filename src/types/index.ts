export interface Case {
  id: string;
  title: string;
  background: string;
  clues: string[];
  objective: string;
  characters: Character[];
}

export interface Character {
  name: string;
  description: string;
  isGuilty?: boolean;
}

export interface DialogueMessage {
  id: string;
  text: string;
  sender: 'user' | 'system';
  timestamp: Date;
}

export interface CaseContextType {
  currentCase: Case | null;
  loading: boolean;
  messages: DialogueMessage[];
  addMessage: (text: string) => void;
  startNewCase: () => void;
}