import { Case } from '../types';

export const sampleCases: Case[] = [
  {
    id: '1',
    title: 'The Missing Heirloom',
    background: 'The wealthy <PERSON> family has reported the theft of a valuable diamond necklace, a family heirloom passed down for generations. The theft occurred during their annual garden party, where only family members and close friends were present.',
    clues: [
      'The security cameras were mysteriously disabled between 7:00 PM and 7:15 PM.',
      'A garden glove with soil stains was found near the safe.',
      'Mrs. <PERSON>\'s niece, <PERSON>, was seen arguing with her aunt about inheritance matters earlier that day.',
      'The family butler, <PERSON>, has recently been making expensive purchases despite his modest salary.',
      'The safe showed no signs of forced entry, suggesting the thief knew the combination.'
    ],
    objective: 'Identify who stole the <PERSON> family\'s diamond necklace and how they did it.',
    characters: [
      {
        name: 'Mrs. <PERSON>',
        description: 'The 68-year-old family matriarch and owner of the necklace. She is known to be forgetful lately.'
      },
      {
        name: '<PERSON>',
        description: 'The family butler who has served the <PERSON><PERSON> for 25 years. Recently purchased a new car.',
        isGuilty: false
      },
      {
        name: '<PERSON>',
        description: 'Mrs. <PERSON>\'s 32-year-old niece who was recently excluded from the will.',
        isGuilty: true
      },
      {
        name: '<PERSON>',
        description: 'Mrs. <PERSON>\'s son who manages the family finances and knows the safe combination.'
      }
    ]
  },
  {
    id: '2',
    title: 'Murder at the Art Gallery',
    background: 'Famous art curator Dr. <PERSON> <PERSON> was found dead in his gallery the morning after a high-profile exhibition opening. The cause of death appears to be blunt force trauma to the head. Four suspects were present at the gallery after hours.',
    clues: [
      'A bloodied sculpture was found near the body, likely the murder weapon.',
      'The gallery\'s assistant, Ms. <PERSON>, claims she saw Dr. <PERSON> arguing with artist <PERSON> <PERSON> over the valuation of his work.',
      'The security log shows that wealthy collector <PERSON> <PERSON>man was the last to leave the gallery at 11:45 PM.',
      'Dr. Bell\'s notebook contained a cryptic message: "Exposed fraud must be addressed tonight."',
      'The gallery\'s financial records show discrepancies that Dr. Bell had recently discovered.'
    ],
    objective: 'Determine who killed Dr. Richard Bell and their motive.',
    characters: [
      {
        name: 'Marcus Reid',
        description: 'An up-and-coming artist whose work was featured in the exhibition. Has a temper and was arguing with the victim.',
        isGuilty: false
      },
      {
        name: 'Ms. Parker',
        description: 'The gallery assistant who has worked with Dr. Bell for 5 years. She was responsible for the gallery\'s financial records.',
        isGuilty: true
      },
      {
        name: 'Victoria Hartman',
        description: 'A wealthy art collector who was interested in acquiring several pieces from the exhibition.',
        isGuilty: false
      },
      {
        name: 'James Wilson',
        description: 'The gallery\'s security guard who was supposedly making rounds during the time of the murder.'
      }
    ]
  },
  {
    id: '3',
    title: 'Corporate Espionage',
    background: 'Tech startup Quantum Innovations is about to launch a revolutionary product, but their prototype and research files have gone missing. The CEO suspects it\'s an inside job, as only key team members had access to the secure lab.',
    clues: [
      'The access logs show that lead engineer David Chen entered the lab at 10:30 PM, though he claims he was at home.',
      'Marketing director Sarah Lopez recently received several large bank deposits from an unknown source.',
      'The company\'s main competitor, TechGiant, announced a similar product just days after the theft.',
      'Security cameras were disabled for "maintenance" the night of the theft, authorized by IT manager Raj Patel.',
      'The janitor reports seeing CFO Michael Warren working unusually late that night.'
    ],
    objective: 'Discover who stole Quantum Innovations\' prototype and research, and determine if corporate espionage was involved.',
    characters: [
      {
        name: 'David Chen',
        description: 'Lead engineer who designed much of the prototype. His access card was used the night of the theft.',
        isGuilty: false
      },
      {
        name: 'Sarah Lopez',
        description: 'Marketing director who has access to all product details for promotional planning.',
        isGuilty: false
      },
      {
        name: 'Raj Patel',
        description: 'IT manager who authorized the security camera maintenance and has been with the company for only 6 months.',
        isGuilty: true
      },
      {
        name: 'Michael Warren',
        description: 'CFO who has been pushing for an earlier product launch to satisfy investors.'
      }
    ]
  }
];

export const getRandomCase = (): Case => {
  const randomIndex = Math.floor(Math.random() * sampleCases.length);
  return sampleCases[randomIndex];
};