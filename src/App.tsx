import React from 'react';
import { CaseProvider } from './contexts/CaseContext';
import { useCase } from './contexts/CaseContext';
import Header from './components/Header';
import CaseBackground from './components/CaseBackground';
import CluesList from './components/CluesList';
import CharactersList from './components/CharactersList';
import DialogueBox from './components/DialogueBox';
import Footer from './components/Footer';
import LoadingScreen from './components/LoadingScreen';

const CaseContent: React.FC = () => {
  const { loading } = useCase();
  
  if (loading) {
    return <LoadingScreen />;
  }
  
  return (
    <div className="flex-1">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <CaseBackground />
            <CluesList />
            <CharactersList />
          </div>
          <div>
            <div className="sticky top-6">
              <DialogueBox />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

function App() {
  return (
    <CaseProvider>
      <div className="flex flex-col min-h-screen bg-gray-100 bg-[url('https://www.transparenttextures.com/patterns/notebook-dark.png')]">
        <Header />
        <CaseContent />
        <Footer />
      </div>
    </CaseProvider>
  );
}

export default App;