import React from 'react';
import { Search } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const LoadingScreen: React.FC = () => {
  const { t } = useTranslation();
  
  return (
    <div className="fixed inset-0 bg-gray-900 bg-opacity-90 flex items-center justify-center z-50">
      <div className="text-center p-8 rounded-lg">
        <div className="flex justify-center mb-6">
          <div className="relative">
            <Search className="text-amber-500 animate-pulse" size={64} />
            <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
              <div className="w-8 h-8 border-t-2 border-b-2 border-amber-300 rounded-full animate-spin"></div>
            </div>
          </div>
        </div>
        <h2 className="text-2xl font-serif font-bold text-white mb-2">{t('loading.title')}</h2>
        <p className="text-gray-300">{t('loading.subtitle')}</p>
      </div>
    </div>
  );
};

export default LoadingScreen