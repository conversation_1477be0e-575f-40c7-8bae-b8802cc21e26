import React from 'react';
import { Verified as Magnifier, FileSearch, Globe } from 'lucide-react';
import { useCase } from '../contexts/CaseContext';
import { useTranslation } from 'react-i18next';

const Header: React.FC = () => {
  const { startNewCase, loading } = useCase();
  const { t, i18n } = useTranslation();
  
  const toggleLanguage = () => {
    const nextLang = i18n.language === 'en' ? 'zh' : 'en';
    i18n.changeLanguage(nextLang);
  };
  
  return (
    <header className="bg-gradient-to-r from-gray-900 to-gray-800 shadow-md">
      <div className="container mx-auto px-4 py-4 flex flex-col md:flex-row items-center justify-between">
        <div className="flex items-center mb-4 md:mb-0">
          <Magnifier className="text-amber-500 mr-2" size={32} />
          <h1 className="text-2xl md:text-3xl font-serif font-bold text-white">
            {t('title')}
          </h1>
        </div>
        
        <div className="flex items-center space-x-4">
          <button
            onClick={toggleLanguage}
            className="flex items-center text-white hover:text-amber-500 transition-colors duration-300"
          >
            <Globe size={20} />
          </button>
          <button 
            onClick={startNewCase}
            disabled={loading}
            className="flex items-center bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-md transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <FileSearch className="mr-2" size={18} />
            {t('newCase')}
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header