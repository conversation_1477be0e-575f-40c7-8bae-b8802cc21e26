import React, { useState } from 'react';
import { Users, ChevronDown, ChevronUp } from 'lucide-react';
import { useCase } from '../contexts/CaseContext';

const CharactersList: React.FC = () => {
  const { currentCase } = useCase();
  const [expanded, setExpanded] = useState(true);
  
  if (!currentCase) return null;
  
  return (
    <div className="bg-purple-50 border-l-4 border-purple-500 p-6 rounded-md shadow-md mb-6 transform transition-all duration-500 hover:shadow-lg">
      <div className="flex items-center justify-between cursor-pointer" onClick={() => setExpanded(!expanded)}>
        <div className="flex items-center">
          <Users className="text-purple-700 mr-3 flex-shrink-0" size={24} />
          <h2 className="text-xl font-serif font-bold text-gray-800">Suspects & Witnesses</h2>
        </div>
        {expanded ? (
          <ChevronUp className="text-gray-500" size={20} />
        ) : (
          <ChevronDown className="text-gray-500" size={20} />
        )}
      </div>
      
      {expanded && (
        <div className="mt-4 pl-9">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {currentCase.characters.map((character, index) => (
              <div 
                key={index} 
                className="bg-white p-4 rounded-md border-l-2 border-purple-300 shadow-sm hover:shadow-md transition-shadow duration-300"
              >
                <h3 className="font-medium text-gray-800 mb-1">{character.name}</h3>
                <p className="text-gray-600 text-sm">{character.description}</p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CharactersList;