import React from 'react';
import { FileText } from 'lucide-react';
import { useCase } from '../contexts/CaseContext';

const CaseBackground: React.FC = () => {
  const { currentCase } = useCase();
  
  if (!currentCase) return null;
  
  return (
    <div className="bg-amber-50 border-l-4 border-amber-500 p-6 rounded-md shadow-md mb-6 transform transition-all duration-500 hover:shadow-lg">
      <div className="flex items-start">
        <FileText className="text-amber-700 mr-3 mt-1 flex-shrink-0" size={24} />
        <div>
          <h2 className="text-xl font-serif font-bold text-gray-800 mb-2">{currentCase.title}</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-md font-medium text-gray-700 mb-1">Background:</h3>
              <p className="text-gray-600">{currentCase.background}</p>
            </div>
            <div>
              <h3 className="text-md font-medium text-gray-700 mb-1">Objective:</h3>
              <p className="text-gray-600">{currentCase.objective}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CaseBackground;