import React from 'react';
import { useTranslation } from 'react-i18next';

const Footer: React.FC = () => {
  const { t } = useTranslation();
  
  return (
    <footer className="bg-gray-800 text-gray-300 py-6 mt-auto">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0">
            <p className="text-sm">
              {t('footer.rights', { year: new Date().getFullYear() })}
            </p>
          </div>
          <div className="flex space-x-4">
            <a 
              href="#" 
              className="text-gray-400 hover:text-amber-500 transition-colors duration-300"
            >
              {t('footer.privacy')}
            </a>
            <a 
              href="#" 
              className="text-gray-400 hover:text-amber-500 transition-colors duration-300"
            >
              {t('footer.terms')}
            </a>
            <a 
              href="#" 
              className="text-gray-400 hover:text-amber-500 transition-colors duration-300"
            >
              {t('footer.contact')}
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer