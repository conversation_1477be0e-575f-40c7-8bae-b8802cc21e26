import React, { useState } from 'react';
import { ClipboardList, ChevronDown, ChevronUp } from 'lucide-react';
import { useCase } from '../contexts/CaseContext';

const CluesList: React.FC = () => {
  const { currentCase } = useCase();
  const [expanded, setExpanded] = useState(true);
  
  if (!currentCase) return null;
  
  return (
    <div className="bg-blue-50 border-l-4 border-blue-500 p-6 rounded-md shadow-md mb-6 transform transition-all duration-500 hover:shadow-lg">
      <div className="flex items-center justify-between cursor-pointer" onClick={() => setExpanded(!expanded)}>
        <div className="flex items-center">
          <ClipboardList className="text-blue-700 mr-3 flex-shrink-0" size={24} />
          <h2 className="text-xl font-serif font-bold text-gray-800">Evidence & Clues</h2>
        </div>
        {expanded ? (
          <ChevronUp className="text-gray-500" size={20} />
        ) : (
          <ChevronDown className="text-gray-500" size={20} />
        )}
      </div>
      
      {expanded && (
        <div className="mt-4 pl-9">
          <ul className="space-y-3">
            {currentCase.clues.map((clue, index) => (
              <li 
                key={index} 
                className="text-gray-600 bg-white p-3 rounded border-l-2 border-blue-300 shadow-sm hover:shadow-md transition-shadow duration-300"
              >
                {clue}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default CluesList;