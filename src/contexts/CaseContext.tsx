import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Case, DialogueMessage, CaseContextType } from '../types';
import { getRandomCase } from '../data/sampleCases';
import { generateAIResponse } from '../services/openai';
import { v4 as uuidv4 } from 'uuid';
import { useTranslation } from 'react-i18next';

const CaseContext = createContext<CaseContextType | undefined>(undefined);

export const useCase = () => {
  const context = useContext(CaseContext);
  if (!context) {
    throw new Error('useCase must be used within a CaseProvider');
  }
  return context;
};

interface CaseProviderProps {
  children: ReactNode;
}

export const CaseProvider: React.FC<CaseProviderProps> = ({ children }) => {
  const [currentCase, setCurrentCase] = useState<Case | null>(null);
  const [loading, setLoading] = useState(true);
  const [messages, setMessages] = useState<DialogueMessage[]>([]);
  const { t } = useTranslation();

  useEffect(() => {
    startNewCase();
  }, []);

  const startNewCase = () => {
    setLoading(true);
    
    setTimeout(() => {
      const newCase = getRandomCase();
      setCurrentCase(newCase);
      
      const initialMessages: DialogueMessage[] = [
        {
          id: uuidv4(),
          text: t('welcome', { title: newCase.title }),
          sender: 'system',
          timestamp: new Date()
        }
      ];
      
      setMessages(initialMessages);
      setLoading(false);
    }, 1000);
  };

  const addMessage = async (text: string) => {
    if (!text.trim() || !currentCase) return;
    
    const userMessage: DialogueMessage = {
      id: uuidv4(),
      text,
      sender: 'user',
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, userMessage]);
    
    try {
      // Convert DialogueMessage array to OpenAI chat format
      const chatHistory = messages.map(msg => ({
        role: msg.sender === 'user' ? 'user' : 'assistant',
        content: msg.text
      }));

      const aiResponse = await generateAIResponse(text, currentCase, chatHistory);
      
      const systemResponse: DialogueMessage = {
        id: uuidv4(),
        text: aiResponse,
        sender: 'system',
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, systemResponse]);
    } catch (error) {
      console.error('Error getting AI response:', error);
      const errorMessage: DialogueMessage = {
        id: uuidv4(),
        text: t('error.aiResponse'),
        sender: 'system',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    }
  };

  const value = {
    currentCase,
    loading,
    messages,
    addMessage,
    startNewCase
  };

  return <CaseContext.Provider value={value}>{children}</CaseContext.Provider>;
};