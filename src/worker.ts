import { getAssetFromKV } from '@cloudflare/kv-asset-handler';

export interface Env {
  __STATIC_CONTENT: KVNamespace;
}

export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    try {
      // Handle static assets
      const response = await getAssetFromKV(
        {
          request,
          waitUntil: ctx.waitUntil.bind(ctx),
        },
        {
          ASSET_NAMESPACE: env.__STATIC_CONTENT,
          ASSET_MANIFEST: JSON.stringify({}),
          cacheControl: {
            browserTTL: 60 * 60 * 24 * 30, // 30 days
            edgeTTL: 60 * 60 * 24 * 30, // 30 days
          },
        }
      );

      // Add security headers
      const newResponse = new Response(response.body, response);
      newResponse.headers.set('X-Content-Type-Options', 'nosniff');
      newResponse.headers.set('X-Frame-Options', 'DENY');
      newResponse.headers.set('X-XSS-Protection', '1; mode=block');
      newResponse.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

      return newResponse;
    } catch (e) {
      // If asset not found, serve index.html for SPA routing
      try {
        const indexRequest = new Request(new URL('/index.html', request.url), request);
        const response = await getAssetFromKV(
          {
            request: indexRequest,
            waitUntil: ctx.waitUntil.bind(ctx),
          },
          {
            ASSET_NAMESPACE: env.__STATIC_CONTENT,
            ASSET_MANIFEST: JSON.stringify({}),
          }
        );

        const newResponse = new Response(response.body, response);
        newResponse.headers.set('Content-Type', 'text/html');
        newResponse.headers.set('X-Content-Type-Options', 'nosniff');
        newResponse.headers.set('X-Frame-Options', 'DENY');
        newResponse.headers.set('X-XSS-Protection', '1; mode=block');
        newResponse.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

        return newResponse;
      } catch (e) {
        return new Response('Not Found', { status: 404 });
      }
    }
  },
};
