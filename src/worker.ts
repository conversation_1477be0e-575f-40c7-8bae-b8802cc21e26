export interface Env {
  ASSETS: Fetcher;
  OPENAI_API_KEY: string;
  AI_API_URL: string;
  AI_MODEL_NAME: string;
}

export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    const url = new URL(request.url);

    // Handle AI chat API proxy
    if (url.pathname === '/api/chat' && request.method === 'POST') {
      try {
        // Check if required environment variables are configured
        if (!env.OPENAI_API_KEY) {
          return new Response(JSON.stringify({
            error: 'API key not configured'
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'POST, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type',
            },
          });
        }

        if (!env.AI_API_URL) {
          return new Response(JSON.stringify({
            error: 'AI API URL not configured'
          }), {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'POST, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type',
            },
          });
        }

        // Get request body
        const body = await request.json() as {
          model?: string;
          messages: Array<{ role: string; content: string }>;
          temperature: number;
          max_tokens: number;
        };

        // Use environment variable for model name if not provided in request
        const modelName = body.model || env.AI_MODEL_NAME || 'deepseek/deepseek-r1-0528';

        // Proxy request to configured AI API
        const response = await fetch(env.AI_API_URL, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${env.OPENAI_API_KEY}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...body,
            model: modelName,
          }),
        });

        const data = await response.json();

        return new Response(JSON.stringify(data), {
          status: response.status,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Referrer-Policy': 'strict-origin-when-cross-origin'
          },
        });
      } catch (error) {
        console.error('Error proxying AI request:', error);
        return new Response(JSON.stringify({
          error: 'Failed to process AI request'
        }), {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        });
      }
    }

    // Handle CORS preflight requests
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      });
    }

    // Handle other API routes
    if (url.pathname.startsWith('/api/')) {
      return new Response(JSON.stringify({
        message: 'API endpoint',
        path: url.pathname
      }), {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'X-Content-Type-Options': 'nosniff',
          'X-Frame-Options': 'DENY',
          'X-XSS-Protection': '1; mode=block',
          'Referrer-Policy': 'strict-origin-when-cross-origin'
        },
      });
    }

    // Serve static assets using the new Static Assets feature
    const response = await env.ASSETS.fetch(request);

    // Add security headers to all responses
    const newResponse = new Response(response.body, response);
    newResponse.headers.set('X-Content-Type-Options', 'nosniff');
    newResponse.headers.set('X-Frame-Options', 'DENY');
    newResponse.headers.set('X-XSS-Protection', '1; mode=block');
    newResponse.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

    return newResponse;
  },
};
