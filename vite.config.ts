import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { createCloudflareViteConfig } from '@cloudflare/workers-sdk/vite';

// https://vitejs.dev/config/
export default defineConfig(createCloudflareViteConfig({
  base: '/detective-play/',
  plugins: [react()],
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
  build: {
    outDir: 'build',
    emptyOutDir: true
  },
  server: {
    port: 3000,
    strictPort: true,
    hmr: {
      clientPort: 443
    }
  }
}));
