# AI 配置说明

本项目支持灵活的 AI 服务配置，可以轻松切换不同的 AI 服务提供商。

## 环境变量配置

### 必需的 Secret 变量（敏感信息）
使用 `wrangler secret put` 命令设置：

```bash
wrangler secret put OPENAI_API_KEY
```

### 公开环境变量
在 `wrangler.toml` 的 `[vars]` 部分配置：

| 变量名 | 描述 | 默认值 | 示例 |
|--------|------|--------|------|
| `AI_API_URL` | AI API 端点 URL | `https://api.novita.ai/v3/openai/chat/completions` | OpenAI 兼容的 API 端点 |
| `AI_MODEL_NAME` | 使用的模型名称 | `deepseek/deepseek-r1-0528` | 任何支持的模型名称 |

## 支持的 AI 服务提供商

### 1. Novita AI（当前配置）
```toml
[vars]
AI_API_URL = "https://api.novita.ai/v3/openai/chat/completions"
AI_MODEL_NAME = "deepseek/deepseek-r1-0528"
```

### 2. OpenAI 官方
```toml
[vars]
AI_API_URL = "https://api.openai.com/v1/chat/completions"
AI_MODEL_NAME = "gpt-4o-mini"
```

### 3. 其他 OpenAI 兼容服务
```toml
[vars]
AI_API_URL = "https://your-api-provider.com/v1/chat/completions"
AI_MODEL_NAME = "your-model-name"
```

## 配置步骤

### 1. 设置 API 密钥（Secret）
```bash
wrangler secret put OPENAI_API_KEY
# 输入您的 API 密钥
```

### 2. 修改 wrangler.toml（如需更换服务商）
编辑 `wrangler.toml` 文件中的 `[vars]` 部分：
```toml
[vars]
AI_API_URL = "您的API端点"
AI_MODEL_NAME = "您的模型名称"
```

### 3. 部署更新
```bash
npm run worker:deploy
```

## 模型推荐

### 成本优化
- **Novita AI**: `deepseek/deepseek-r1-0528` - 性价比高
- **OpenAI**: `gpt-4o-mini` - 官方轻量版

### 性能优先
- **OpenAI**: `gpt-4o` - 最新最强
- **Anthropic**: `claude-3-5-sonnet` - 推理能力强

### 多语言支持
- **DeepSeek**: 中英文表现优秀
- **GPT-4**: 多语言支持全面

## 故障排除

### 常见错误
1. **API key not configured**: 需要设置 `OPENAI_API_KEY`
2. **AI API URL not configured**: 检查 `wrangler.toml` 中的 `AI_API_URL`
3. **Model not found**: 确认模型名称正确且服务商支持

### 调试方法
1. 检查 Worker 日志：`wrangler tail`
2. 验证环境变量：在 Cloudflare Dashboard 查看
3. 测试 API 端点：使用 curl 或 Postman 直接测试

## 安全注意事项

- ✅ API 密钥存储为 Secret，不会暴露在代码中
- ✅ 所有 AI 请求通过 Worker 代理，避免 CORS 问题
- ✅ 前端无法直接访问 API 密钥
- ⚠️ 确保选择的 AI 服务商符合您的数据隐私要求
