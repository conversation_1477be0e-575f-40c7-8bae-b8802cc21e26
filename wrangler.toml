name = "detective-play"
compatibility_date = "2025-05-31"
main = "src/worker.ts"

[build]
command = "npm run build"

[assets]
directory = "./dist"
binding = "ASSETS"
not_found_handling = "single-page-application"

# Environment variables (non-secret)
[vars]
AI_API_URL = "https://api.novita.ai/v3/openai/chat/completions"
AI_MODEL_NAME = "deepseek/deepseek-v3-0324"

# Secrets (use 'wrangler secret put' to set these)
# OPENAI_API_KEY = "your-api-key-here"
