var Xm=Object.defineProperty;var Gm=(t,e,n)=>e in t?Xm(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var oc=(t,e,n)=>Gm(t,typeof e!="symbol"?e+"":e,n);(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const a of i.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();var od={exports:{}},ia={},ld={exports:{}},D={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xs=Symbol.for("react.element"),Ym=Symbol.for("react.portal"),qm=Symbol.for("react.fragment"),Zm=Symbol.for("react.strict_mode"),eg=Symbol.for("react.profiler"),tg=Symbol.for("react.provider"),ng=Symbol.for("react.context"),rg=Symbol.for("react.forward_ref"),sg=Symbol.for("react.suspense"),ig=Symbol.for("react.memo"),ag=Symbol.for("react.lazy"),lc=Symbol.iterator;function og(t){return t===null||typeof t!="object"?null:(t=lc&&t[lc]||t["@@iterator"],typeof t=="function"?t:null)}var ud={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},cd=Object.assign,fd={};function hr(t,e,n){this.props=t,this.context=e,this.refs=fd,this.updater=n||ud}hr.prototype.isReactComponent={};hr.prototype.setState=function(t,e){if(typeof t!="object"&&typeof t!="function"&&t!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,e,"setState")};hr.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")};function dd(){}dd.prototype=hr.prototype;function Ol(t,e,n){this.props=t,this.context=e,this.refs=fd,this.updater=n||ud}var Al=Ol.prototype=new dd;Al.constructor=Ol;cd(Al,hr.prototype);Al.isPureReactComponent=!0;var uc=Array.isArray,hd=Object.prototype.hasOwnProperty,Il={current:null},pd={key:!0,ref:!0,__self:!0,__source:!0};function md(t,e,n){var r,s={},i=null,a=null;if(e!=null)for(r in e.ref!==void 0&&(a=e.ref),e.key!==void 0&&(i=""+e.key),e)hd.call(e,r)&&!pd.hasOwnProperty(r)&&(s[r]=e[r]);var o=arguments.length-2;if(o===1)s.children=n;else if(1<o){for(var l=Array(o),u=0;u<o;u++)l[u]=arguments[u+2];s.children=l}if(t&&t.defaultProps)for(r in o=t.defaultProps,o)s[r]===void 0&&(s[r]=o[r]);return{$$typeof:xs,type:t,key:i,ref:a,props:s,_owner:Il.current}}function lg(t,e){return{$$typeof:xs,type:t.type,key:e,ref:t.ref,props:t.props,_owner:t._owner}}function bl(t){return typeof t=="object"&&t!==null&&t.$$typeof===xs}function ug(t){var e={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(n){return e[n]})}var cc=/\/+/g;function Ma(t,e){return typeof t=="object"&&t!==null&&t.key!=null?ug(""+t.key):e.toString(36)}function ui(t,e,n,r,s){var i=typeof t;(i==="undefined"||i==="boolean")&&(t=null);var a=!1;if(t===null)a=!0;else switch(i){case"string":case"number":a=!0;break;case"object":switch(t.$$typeof){case xs:case Ym:a=!0}}if(a)return a=t,s=s(a),t=r===""?"."+Ma(a,0):r,uc(s)?(n="",t!=null&&(n=t.replace(cc,"$&/")+"/"),ui(s,e,n,"",function(u){return u})):s!=null&&(bl(s)&&(s=lg(s,n+(!s.key||a&&a.key===s.key?"":(""+s.key).replace(cc,"$&/")+"/")+t)),e.push(s)),1;if(a=0,r=r===""?".":r+":",uc(t))for(var o=0;o<t.length;o++){i=t[o];var l=r+Ma(i,o);a+=ui(i,e,n,l,s)}else if(l=og(t),typeof l=="function")for(t=l.call(t),o=0;!(i=t.next()).done;)i=i.value,l=r+Ma(i,o++),a+=ui(i,e,n,l,s);else if(i==="object")throw e=String(t),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");return a}function Ts(t,e,n){if(t==null)return t;var r=[],s=0;return ui(t,r,"","",function(i){return e.call(n,i,s++)}),r}function cg(t){if(t._status===-1){var e=t._result;e=e(),e.then(function(n){(t._status===0||t._status===-1)&&(t._status=1,t._result=n)},function(n){(t._status===0||t._status===-1)&&(t._status=2,t._result=n)}),t._status===-1&&(t._status=0,t._result=e)}if(t._status===1)return t._result.default;throw t._result}var Pe={current:null},ci={transition:null},fg={ReactCurrentDispatcher:Pe,ReactCurrentBatchConfig:ci,ReactCurrentOwner:Il};function gd(){throw Error("act(...) is not supported in production builds of React.")}D.Children={map:Ts,forEach:function(t,e,n){Ts(t,function(){e.apply(this,arguments)},n)},count:function(t){var e=0;return Ts(t,function(){e++}),e},toArray:function(t){return Ts(t,function(e){return e})||[]},only:function(t){if(!bl(t))throw Error("React.Children.only expected to receive a single React element child.");return t}};D.Component=hr;D.Fragment=qm;D.Profiler=eg;D.PureComponent=Ol;D.StrictMode=Zm;D.Suspense=sg;D.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=fg;D.act=gd;D.cloneElement=function(t,e,n){if(t==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+t+".");var r=cd({},t.props),s=t.key,i=t.ref,a=t._owner;if(e!=null){if(e.ref!==void 0&&(i=e.ref,a=Il.current),e.key!==void 0&&(s=""+e.key),t.type&&t.type.defaultProps)var o=t.type.defaultProps;for(l in e)hd.call(e,l)&&!pd.hasOwnProperty(l)&&(r[l]=e[l]===void 0&&o!==void 0?o[l]:e[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){o=Array(l);for(var u=0;u<l;u++)o[u]=arguments[u+2];r.children=o}return{$$typeof:xs,type:t.type,key:s,ref:i,props:r,_owner:a}};D.createContext=function(t){return t={$$typeof:ng,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},t.Provider={$$typeof:tg,_context:t},t.Consumer=t};D.createElement=md;D.createFactory=function(t){var e=md.bind(null,t);return e.type=t,e};D.createRef=function(){return{current:null}};D.forwardRef=function(t){return{$$typeof:rg,render:t}};D.isValidElement=bl;D.lazy=function(t){return{$$typeof:ag,_payload:{_status:-1,_result:t},_init:cg}};D.memo=function(t,e){return{$$typeof:ig,type:t,compare:e===void 0?null:e}};D.startTransition=function(t){var e=ci.transition;ci.transition={};try{t()}finally{ci.transition=e}};D.unstable_act=gd;D.useCallback=function(t,e){return Pe.current.useCallback(t,e)};D.useContext=function(t){return Pe.current.useContext(t)};D.useDebugValue=function(){};D.useDeferredValue=function(t){return Pe.current.useDeferredValue(t)};D.useEffect=function(t,e){return Pe.current.useEffect(t,e)};D.useId=function(){return Pe.current.useId()};D.useImperativeHandle=function(t,e,n){return Pe.current.useImperativeHandle(t,e,n)};D.useInsertionEffect=function(t,e){return Pe.current.useInsertionEffect(t,e)};D.useLayoutEffect=function(t,e){return Pe.current.useLayoutEffect(t,e)};D.useMemo=function(t,e){return Pe.current.useMemo(t,e)};D.useReducer=function(t,e,n){return Pe.current.useReducer(t,e,n)};D.useRef=function(t){return Pe.current.useRef(t)};D.useState=function(t){return Pe.current.useState(t)};D.useSyncExternalStore=function(t,e,n){return Pe.current.useSyncExternalStore(t,e,n)};D.useTransition=function(){return Pe.current.useTransition()};D.version="18.3.1";ld.exports=D;var V=ld.exports;/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dg=V,hg=Symbol.for("react.element"),pg=Symbol.for("react.fragment"),mg=Object.prototype.hasOwnProperty,gg=dg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,yg={key:!0,ref:!0,__self:!0,__source:!0};function yd(t,e,n){var r,s={},i=null,a=null;n!==void 0&&(i=""+n),e.key!==void 0&&(i=""+e.key),e.ref!==void 0&&(a=e.ref);for(r in e)mg.call(e,r)&&!yg.hasOwnProperty(r)&&(s[r]=e[r]);if(t&&t.defaultProps)for(r in e=t.defaultProps,e)s[r]===void 0&&(s[r]=e[r]);return{$$typeof:hg,type:t,key:i,ref:a,props:s,_owner:gg.current}}ia.Fragment=pg;ia.jsx=yd;ia.jsxs=yd;od.exports=ia;var C=od.exports,vd={exports:{}},Be={},wd={exports:{}},xd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(t){function e(P,A){var M=P.length;P.push(A);e:for(;0<M;){var Q=M-1>>>1,le=P[Q];if(0<s(le,A))P[Q]=A,P[M]=le,M=Q;else break e}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var A=P[0],M=P.pop();if(M!==A){P[0]=M;e:for(var Q=0,le=P.length,Is=le>>>1;Q<Is;){var on=2*(Q+1)-1,Fa=P[on],ln=on+1,bs=P[ln];if(0>s(Fa,M))ln<le&&0>s(bs,Fa)?(P[Q]=bs,P[ln]=M,Q=ln):(P[Q]=Fa,P[on]=M,Q=on);else if(ln<le&&0>s(bs,M))P[Q]=bs,P[ln]=M,Q=ln;else break e}}return A}function s(P,A){var M=P.sortIndex-A.sortIndex;return M!==0?M:P.id-A.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;t.unstable_now=function(){return i.now()}}else{var a=Date,o=a.now();t.unstable_now=function(){return a.now()-o}}var l=[],u=[],h=1,d=null,c=3,g=!1,v=!1,w=!1,_=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,f=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function p(P){for(var A=n(u);A!==null;){if(A.callback===null)r(u);else if(A.startTime<=P)r(u),A.sortIndex=A.expirationTime,e(l,A);else break;A=n(u)}}function y(P){if(w=!1,p(P),!v)if(n(l)!==null)v=!0,bt(k);else{var A=n(u);A!==null&&Nn(y,A.startTime-P)}}function k(P,A){v=!1,w&&(w=!1,m(R),R=-1),g=!0;var M=c;try{for(p(A),d=n(l);d!==null&&(!(d.expirationTime>A)||P&&!T());){var Q=d.callback;if(typeof Q=="function"){d.callback=null,c=d.priorityLevel;var le=Q(d.expirationTime<=A);A=t.unstable_now(),typeof le=="function"?d.callback=le:d===n(l)&&r(l),p(A)}else r(l);d=n(l)}if(d!==null)var Is=!0;else{var on=n(u);on!==null&&Nn(y,on.startTime-A),Is=!1}return Is}finally{d=null,c=M,g=!1}}var S=!1,x=null,R=-1,F=5,L=-1;function T(){return!(t.unstable_now()-L<F)}function Te(){if(x!==null){var P=t.unstable_now();L=P;var A=!0;try{A=x(!0,P)}finally{A?Ye():(S=!1,x=null)}}else S=!1}var Ye;if(typeof f=="function")Ye=function(){f(Te)};else if(typeof MessageChannel<"u"){var ut=new MessageChannel,ct=ut.port2;ut.port1.onmessage=Te,Ye=function(){ct.postMessage(null)}}else Ye=function(){_(Te,0)};function bt(P){x=P,S||(S=!0,Ye())}function Nn(P,A){R=_(function(){P(t.unstable_now())},A)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(P){P.callback=null},t.unstable_continueExecution=function(){v||g||(v=!0,bt(k))},t.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):F=0<P?Math.floor(1e3/P):5},t.unstable_getCurrentPriorityLevel=function(){return c},t.unstable_getFirstCallbackNode=function(){return n(l)},t.unstable_next=function(P){switch(c){case 1:case 2:case 3:var A=3;break;default:A=c}var M=c;c=A;try{return P()}finally{c=M}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(P,A){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var M=c;c=P;try{return A()}finally{c=M}},t.unstable_scheduleCallback=function(P,A,M){var Q=t.unstable_now();switch(typeof M=="object"&&M!==null?(M=M.delay,M=typeof M=="number"&&0<M?Q+M:Q):M=Q,P){case 1:var le=-1;break;case 2:le=250;break;case 5:le=**********;break;case 4:le=1e4;break;default:le=5e3}return le=M+le,P={id:h++,callback:A,priorityLevel:P,startTime:M,expirationTime:le,sortIndex:-1},M>Q?(P.sortIndex=M,e(u,P),n(l)===null&&P===n(u)&&(w?(m(R),R=-1):w=!0,Nn(y,M-Q))):(P.sortIndex=le,e(l,P),v||g||(v=!0,bt(k))),P},t.unstable_shouldYield=T,t.unstable_wrapCallback=function(P){var A=c;return function(){var M=c;c=A;try{return P.apply(this,arguments)}finally{c=M}}}})(xd);wd.exports=xd;var vg=wd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wg=V,Ue=vg;function E(t){for(var e="https://reactjs.org/docs/error-decoder.html?invariant="+t,n=1;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var _d=new Set,Zr={};function Pn(t,e){ar(t,e),ar(t+"Capture",e)}function ar(t,e){for(Zr[t]=e,t=0;t<e.length;t++)_d.add(e[t])}var Nt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),So=Object.prototype.hasOwnProperty,xg=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,fc={},dc={};function _g(t){return So.call(dc,t)?!0:So.call(fc,t)?!1:xg.test(t)?dc[t]=!0:(fc[t]=!0,!1)}function Sg(t,e,n,r){if(n!==null&&n.type===0)return!1;switch(typeof e){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(t=t.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-");default:return!1}}function kg(t,e,n,r){if(e===null||typeof e>"u"||Sg(t,e,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!e;case 4:return e===!1;case 5:return isNaN(e);case 6:return isNaN(e)||1>e}return!1}function Re(t,e,n,r,s,i,a){this.acceptsBooleans=e===2||e===3||e===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=t,this.type=e,this.sanitizeURL=i,this.removeEmptyString=a}var ge={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(t){ge[t]=new Re(t,0,!1,t,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(t){var e=t[0];ge[e]=new Re(e,1,!1,t[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(t){ge[t]=new Re(t,2,!1,t.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(t){ge[t]=new Re(t,2,!1,t,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(t){ge[t]=new Re(t,3,!1,t.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(t){ge[t]=new Re(t,3,!0,t,null,!1,!1)});["capture","download"].forEach(function(t){ge[t]=new Re(t,4,!1,t,null,!1,!1)});["cols","rows","size","span"].forEach(function(t){ge[t]=new Re(t,6,!1,t,null,!1,!1)});["rowSpan","start"].forEach(function(t){ge[t]=new Re(t,5,!1,t.toLowerCase(),null,!1,!1)});var Tl=/[\-:]([a-z])/g;function $l(t){return t[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(t){var e=t.replace(Tl,$l);ge[e]=new Re(e,1,!1,t,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(t){var e=t.replace(Tl,$l);ge[e]=new Re(e,1,!1,t,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(t){var e=t.replace(Tl,$l);ge[e]=new Re(e,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(t){ge[t]=new Re(t,1,!1,t.toLowerCase(),null,!1,!1)});ge.xlinkHref=new Re("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(t){ge[t]=new Re(t,1,!1,t.toLowerCase(),null,!0,!0)});function Fl(t,e,n,r){var s=ge.hasOwnProperty(e)?ge[e]:null;(s!==null?s.type!==0:r||!(2<e.length)||e[0]!=="o"&&e[0]!=="O"||e[1]!=="n"&&e[1]!=="N")&&(kg(e,n,s,r)&&(n=null),r||s===null?_g(e)&&(n===null?t.removeAttribute(e):t.setAttribute(e,""+n)):s.mustUseProperty?t[s.propertyName]=n===null?s.type===3?!1:"":n:(e=s.attributeName,r=s.attributeNamespace,n===null?t.removeAttribute(e):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?t.setAttributeNS(r,e,n):t.setAttribute(e,n))))}var It=wg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,$s=Symbol.for("react.element"),jn=Symbol.for("react.portal"),Dn=Symbol.for("react.fragment"),Ml=Symbol.for("react.strict_mode"),ko=Symbol.for("react.profiler"),Sd=Symbol.for("react.provider"),kd=Symbol.for("react.context"),jl=Symbol.for("react.forward_ref"),Co=Symbol.for("react.suspense"),Eo=Symbol.for("react.suspense_list"),Dl=Symbol.for("react.memo"),jt=Symbol.for("react.lazy"),Cd=Symbol.for("react.offscreen"),hc=Symbol.iterator;function xr(t){return t===null||typeof t!="object"?null:(t=hc&&t[hc]||t["@@iterator"],typeof t=="function"?t:null)}var q=Object.assign,ja;function Ar(t){if(ja===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);ja=e&&e[1]||""}return`
`+ja+t}var Da=!1;function za(t,e){if(!t||Da)return"";Da=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(e)if(e=function(){throw Error()},Object.defineProperty(e.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(e,[])}catch(u){var r=u}Reflect.construct(t,[],e)}else{try{e.call()}catch(u){r=u}t.call(e.prototype)}else{try{throw Error()}catch(u){r=u}t()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var s=u.stack.split(`
`),i=r.stack.split(`
`),a=s.length-1,o=i.length-1;1<=a&&0<=o&&s[a]!==i[o];)o--;for(;1<=a&&0<=o;a--,o--)if(s[a]!==i[o]){if(a!==1||o!==1)do if(a--,o--,0>o||s[a]!==i[o]){var l=`
`+s[a].replace(" at new "," at ");return t.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",t.displayName)),l}while(1<=a&&0<=o);break}}}finally{Da=!1,Error.prepareStackTrace=n}return(t=t?t.displayName||t.name:"")?Ar(t):""}function Cg(t){switch(t.tag){case 5:return Ar(t.type);case 16:return Ar("Lazy");case 13:return Ar("Suspense");case 19:return Ar("SuspenseList");case 0:case 2:case 15:return t=za(t.type,!1),t;case 11:return t=za(t.type.render,!1),t;case 1:return t=za(t.type,!0),t;default:return""}}function Po(t){if(t==null)return null;if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case Dn:return"Fragment";case jn:return"Portal";case ko:return"Profiler";case Ml:return"StrictMode";case Co:return"Suspense";case Eo:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case kd:return(t.displayName||"Context")+".Consumer";case Sd:return(t._context.displayName||"Context")+".Provider";case jl:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Dl:return e=t.displayName||null,e!==null?e:Po(t.type)||"Memo";case jt:e=t._payload,t=t._init;try{return Po(t(e))}catch{}}return null}function Eg(t){var e=t.type;switch(t.tag){case 24:return"Cache";case 9:return(e.displayName||"Context")+".Consumer";case 10:return(e._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return t=e.render,t=t.displayName||t.name||"",e.displayName||(t!==""?"ForwardRef("+t+")":"ForwardRef");case 7:return"Fragment";case 5:return e;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Po(e);case 8:return e===Ml?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e}return null}function Zt(t){switch(typeof t){case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Ed(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Pg(t){var e=Ed(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),r=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,i=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return s.call(this)},set:function(a){r=""+a,i.call(this,a)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Fs(t){t._valueTracker||(t._valueTracker=Pg(t))}function Pd(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),r="";return t&&(r=Ed(t)?t.checked?"true":"false":t.value),t=r,t!==n?(e.setValue(t),!0):!1}function Ri(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}function Ro(t,e){var n=e.checked;return q({},e,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??t._wrapperState.initialChecked})}function pc(t,e){var n=e.defaultValue==null?"":e.defaultValue,r=e.checked!=null?e.checked:e.defaultChecked;n=Zt(e.value!=null?e.value:n),t._wrapperState={initialChecked:r,initialValue:n,controlled:e.type==="checkbox"||e.type==="radio"?e.checked!=null:e.value!=null}}function Rd(t,e){e=e.checked,e!=null&&Fl(t,"checked",e,!1)}function No(t,e){Rd(t,e);var n=Zt(e.value),r=e.type;if(n!=null)r==="number"?(n===0&&t.value===""||t.value!=n)&&(t.value=""+n):t.value!==""+n&&(t.value=""+n);else if(r==="submit"||r==="reset"){t.removeAttribute("value");return}e.hasOwnProperty("value")?Lo(t,e.type,n):e.hasOwnProperty("defaultValue")&&Lo(t,e.type,Zt(e.defaultValue)),e.checked==null&&e.defaultChecked!=null&&(t.defaultChecked=!!e.defaultChecked)}function mc(t,e,n){if(e.hasOwnProperty("value")||e.hasOwnProperty("defaultValue")){var r=e.type;if(!(r!=="submit"&&r!=="reset"||e.value!==void 0&&e.value!==null))return;e=""+t._wrapperState.initialValue,n||e===t.value||(t.value=e),t.defaultValue=e}n=t.name,n!==""&&(t.name=""),t.defaultChecked=!!t._wrapperState.initialChecked,n!==""&&(t.name=n)}function Lo(t,e,n){(e!=="number"||Ri(t.ownerDocument)!==t)&&(n==null?t.defaultValue=""+t._wrapperState.initialValue:t.defaultValue!==""+n&&(t.defaultValue=""+n))}var Ir=Array.isArray;function Yn(t,e,n,r){if(t=t.options,e){e={};for(var s=0;s<n.length;s++)e["$"+n[s]]=!0;for(n=0;n<t.length;n++)s=e.hasOwnProperty("$"+t[n].value),t[n].selected!==s&&(t[n].selected=s),s&&r&&(t[n].defaultSelected=!0)}else{for(n=""+Zt(n),e=null,s=0;s<t.length;s++){if(t[s].value===n){t[s].selected=!0,r&&(t[s].defaultSelected=!0);return}e!==null||t[s].disabled||(e=t[s])}e!==null&&(e.selected=!0)}}function Oo(t,e){if(e.dangerouslySetInnerHTML!=null)throw Error(E(91));return q({},e,{value:void 0,defaultValue:void 0,children:""+t._wrapperState.initialValue})}function gc(t,e){var n=e.value;if(n==null){if(n=e.children,e=e.defaultValue,n!=null){if(e!=null)throw Error(E(92));if(Ir(n)){if(1<n.length)throw Error(E(93));n=n[0]}e=n}e==null&&(e=""),n=e}t._wrapperState={initialValue:Zt(n)}}function Nd(t,e){var n=Zt(e.value),r=Zt(e.defaultValue);n!=null&&(n=""+n,n!==t.value&&(t.value=n),e.defaultValue==null&&t.defaultValue!==n&&(t.defaultValue=n)),r!=null&&(t.defaultValue=""+r)}function yc(t){var e=t.textContent;e===t._wrapperState.initialValue&&e!==""&&e!==null&&(t.value=e)}function Ld(t){switch(t){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ao(t,e){return t==null||t==="http://www.w3.org/1999/xhtml"?Ld(e):t==="http://www.w3.org/2000/svg"&&e==="foreignObject"?"http://www.w3.org/1999/xhtml":t}var Ms,Od=function(t){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(e,n,r,s){MSApp.execUnsafeLocalFunction(function(){return t(e,n,r,s)})}:t}(function(t,e){if(t.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in t)t.innerHTML=e;else{for(Ms=Ms||document.createElement("div"),Ms.innerHTML="<svg>"+e.valueOf().toString()+"</svg>",e=Ms.firstChild;t.firstChild;)t.removeChild(t.firstChild);for(;e.firstChild;)t.appendChild(e.firstChild)}});function es(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var Ur={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Rg=["Webkit","ms","Moz","O"];Object.keys(Ur).forEach(function(t){Rg.forEach(function(e){e=e+t.charAt(0).toUpperCase()+t.substring(1),Ur[e]=Ur[t]})});function Ad(t,e,n){return e==null||typeof e=="boolean"||e===""?"":n||typeof e!="number"||e===0||Ur.hasOwnProperty(t)&&Ur[t]?(""+e).trim():e+"px"}function Id(t,e){t=t.style;for(var n in e)if(e.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=Ad(n,e[n],r);n==="float"&&(n="cssFloat"),r?t.setProperty(n,s):t[n]=s}}var Ng=q({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Io(t,e){if(e){if(Ng[t]&&(e.children!=null||e.dangerouslySetInnerHTML!=null))throw Error(E(137,t));if(e.dangerouslySetInnerHTML!=null){if(e.children!=null)throw Error(E(60));if(typeof e.dangerouslySetInnerHTML!="object"||!("__html"in e.dangerouslySetInnerHTML))throw Error(E(61))}if(e.style!=null&&typeof e.style!="object")throw Error(E(62))}}function bo(t,e){if(t.indexOf("-")===-1)return typeof e.is=="string";switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var To=null;function zl(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var $o=null,qn=null,Zn=null;function vc(t){if(t=ks(t)){if(typeof $o!="function")throw Error(E(280));var e=t.stateNode;e&&(e=ca(e),$o(t.stateNode,t.type,e))}}function bd(t){qn?Zn?Zn.push(t):Zn=[t]:qn=t}function Td(){if(qn){var t=qn,e=Zn;if(Zn=qn=null,vc(t),e)for(t=0;t<e.length;t++)vc(e[t])}}function $d(t,e){return t(e)}function Fd(){}var Ua=!1;function Md(t,e,n){if(Ua)return t(e,n);Ua=!0;try{return $d(t,e,n)}finally{Ua=!1,(qn!==null||Zn!==null)&&(Fd(),Td())}}function ts(t,e){var n=t.stateNode;if(n===null)return null;var r=ca(n);if(r===null)return null;n=r[e];e:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(t=t.type,r=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!r;break e;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(E(231,e,typeof n));return n}var Fo=!1;if(Nt)try{var _r={};Object.defineProperty(_r,"passive",{get:function(){Fo=!0}}),window.addEventListener("test",_r,_r),window.removeEventListener("test",_r,_r)}catch{Fo=!1}function Lg(t,e,n,r,s,i,a,o,l){var u=Array.prototype.slice.call(arguments,3);try{e.apply(n,u)}catch(h){this.onError(h)}}var Br=!1,Ni=null,Li=!1,Mo=null,Og={onError:function(t){Br=!0,Ni=t}};function Ag(t,e,n,r,s,i,a,o,l){Br=!1,Ni=null,Lg.apply(Og,arguments)}function Ig(t,e,n,r,s,i,a,o,l){if(Ag.apply(this,arguments),Br){if(Br){var u=Ni;Br=!1,Ni=null}else throw Error(E(198));Li||(Li=!0,Mo=u)}}function Rn(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,e.flags&4098&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function jd(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function wc(t){if(Rn(t)!==t)throw Error(E(188))}function bg(t){var e=t.alternate;if(!e){if(e=Rn(t),e===null)throw Error(E(188));return e!==t?null:t}for(var n=t,r=e;;){var s=n.return;if(s===null)break;var i=s.alternate;if(i===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===i.child){for(i=s.child;i;){if(i===n)return wc(s),t;if(i===r)return wc(s),e;i=i.sibling}throw Error(E(188))}if(n.return!==r.return)n=s,r=i;else{for(var a=!1,o=s.child;o;){if(o===n){a=!0,n=s,r=i;break}if(o===r){a=!0,r=s,n=i;break}o=o.sibling}if(!a){for(o=i.child;o;){if(o===n){a=!0,n=i,r=s;break}if(o===r){a=!0,r=i,n=s;break}o=o.sibling}if(!a)throw Error(E(189))}}if(n.alternate!==r)throw Error(E(190))}if(n.tag!==3)throw Error(E(188));return n.stateNode.current===n?t:e}function Dd(t){return t=bg(t),t!==null?zd(t):null}function zd(t){if(t.tag===5||t.tag===6)return t;for(t=t.child;t!==null;){var e=zd(t);if(e!==null)return e;t=t.sibling}return null}var Ud=Ue.unstable_scheduleCallback,xc=Ue.unstable_cancelCallback,Tg=Ue.unstable_shouldYield,$g=Ue.unstable_requestPaint,te=Ue.unstable_now,Fg=Ue.unstable_getCurrentPriorityLevel,Ul=Ue.unstable_ImmediatePriority,Bd=Ue.unstable_UserBlockingPriority,Oi=Ue.unstable_NormalPriority,Mg=Ue.unstable_LowPriority,Vd=Ue.unstable_IdlePriority,aa=null,wt=null;function jg(t){if(wt&&typeof wt.onCommitFiberRoot=="function")try{wt.onCommitFiberRoot(aa,t,void 0,(t.current.flags&128)===128)}catch{}}var it=Math.clz32?Math.clz32:Ug,Dg=Math.log,zg=Math.LN2;function Ug(t){return t>>>=0,t===0?32:31-(Dg(t)/zg|0)|0}var js=64,Ds=4194304;function br(t){switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return t&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return t}}function Ai(t,e){var n=t.pendingLanes;if(n===0)return 0;var r=0,s=t.suspendedLanes,i=t.pingedLanes,a=n&268435455;if(a!==0){var o=a&~s;o!==0?r=br(o):(i&=a,i!==0&&(r=br(i)))}else a=n&~s,a!==0?r=br(a):i!==0&&(r=br(i));if(r===0)return 0;if(e!==0&&e!==r&&!(e&s)&&(s=r&-r,i=e&-e,s>=i||s===16&&(i&4194240)!==0))return e;if(r&4&&(r|=n&16),e=t.entangledLanes,e!==0)for(t=t.entanglements,e&=r;0<e;)n=31-it(e),s=1<<n,r|=t[n],e&=~s;return r}function Bg(t,e){switch(t){case 1:case 2:case 4:return e+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Vg(t,e){for(var n=t.suspendedLanes,r=t.pingedLanes,s=t.expirationTimes,i=t.pendingLanes;0<i;){var a=31-it(i),o=1<<a,l=s[a];l===-1?(!(o&n)||o&r)&&(s[a]=Bg(o,e)):l<=e&&(t.expiredLanes|=o),i&=~o}}function jo(t){return t=t.pendingLanes&-1073741825,t!==0?t:t&1073741824?1073741824:0}function Hd(){var t=js;return js<<=1,!(js&4194240)&&(js=64),t}function Ba(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function _s(t,e,n){t.pendingLanes|=e,e!==536870912&&(t.suspendedLanes=0,t.pingedLanes=0),t=t.eventTimes,e=31-it(e),t[e]=n}function Hg(t,e){var n=t.pendingLanes&~e;t.pendingLanes=e,t.suspendedLanes=0,t.pingedLanes=0,t.expiredLanes&=e,t.mutableReadLanes&=e,t.entangledLanes&=e,e=t.entanglements;var r=t.eventTimes;for(t=t.expirationTimes;0<n;){var s=31-it(n),i=1<<s;e[s]=0,r[s]=-1,t[s]=-1,n&=~i}}function Bl(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var r=31-it(n),s=1<<r;s&e|t[r]&e&&(t[r]|=e),n&=~s}}var U=0;function Wd(t){return t&=-t,1<t?4<t?t&268435455?16:536870912:4:1}var Kd,Vl,Qd,Jd,Xd,Do=!1,zs=[],Ht=null,Wt=null,Kt=null,ns=new Map,rs=new Map,zt=[],Wg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function _c(t,e){switch(t){case"focusin":case"focusout":Ht=null;break;case"dragenter":case"dragleave":Wt=null;break;case"mouseover":case"mouseout":Kt=null;break;case"pointerover":case"pointerout":ns.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":rs.delete(e.pointerId)}}function Sr(t,e,n,r,s,i){return t===null||t.nativeEvent!==i?(t={blockedOn:e,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[s]},e!==null&&(e=ks(e),e!==null&&Vl(e)),t):(t.eventSystemFlags|=r,e=t.targetContainers,s!==null&&e.indexOf(s)===-1&&e.push(s),t)}function Kg(t,e,n,r,s){switch(e){case"focusin":return Ht=Sr(Ht,t,e,n,r,s),!0;case"dragenter":return Wt=Sr(Wt,t,e,n,r,s),!0;case"mouseover":return Kt=Sr(Kt,t,e,n,r,s),!0;case"pointerover":var i=s.pointerId;return ns.set(i,Sr(ns.get(i)||null,t,e,n,r,s)),!0;case"gotpointercapture":return i=s.pointerId,rs.set(i,Sr(rs.get(i)||null,t,e,n,r,s)),!0}return!1}function Gd(t){var e=hn(t.target);if(e!==null){var n=Rn(e);if(n!==null){if(e=n.tag,e===13){if(e=jd(n),e!==null){t.blockedOn=e,Xd(t.priority,function(){Qd(n)});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function fi(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=zo(t.domEventName,t.eventSystemFlags,e[0],t.nativeEvent);if(n===null){n=t.nativeEvent;var r=new n.constructor(n.type,n);To=r,n.target.dispatchEvent(r),To=null}else return e=ks(n),e!==null&&Vl(e),t.blockedOn=n,!1;e.shift()}return!0}function Sc(t,e,n){fi(t)&&n.delete(e)}function Qg(){Do=!1,Ht!==null&&fi(Ht)&&(Ht=null),Wt!==null&&fi(Wt)&&(Wt=null),Kt!==null&&fi(Kt)&&(Kt=null),ns.forEach(Sc),rs.forEach(Sc)}function kr(t,e){t.blockedOn===e&&(t.blockedOn=null,Do||(Do=!0,Ue.unstable_scheduleCallback(Ue.unstable_NormalPriority,Qg)))}function ss(t){function e(s){return kr(s,t)}if(0<zs.length){kr(zs[0],t);for(var n=1;n<zs.length;n++){var r=zs[n];r.blockedOn===t&&(r.blockedOn=null)}}for(Ht!==null&&kr(Ht,t),Wt!==null&&kr(Wt,t),Kt!==null&&kr(Kt,t),ns.forEach(e),rs.forEach(e),n=0;n<zt.length;n++)r=zt[n],r.blockedOn===t&&(r.blockedOn=null);for(;0<zt.length&&(n=zt[0],n.blockedOn===null);)Gd(n),n.blockedOn===null&&zt.shift()}var er=It.ReactCurrentBatchConfig,Ii=!0;function Jg(t,e,n,r){var s=U,i=er.transition;er.transition=null;try{U=1,Hl(t,e,n,r)}finally{U=s,er.transition=i}}function Xg(t,e,n,r){var s=U,i=er.transition;er.transition=null;try{U=4,Hl(t,e,n,r)}finally{U=s,er.transition=i}}function Hl(t,e,n,r){if(Ii){var s=zo(t,e,n,r);if(s===null)qa(t,e,r,bi,n),_c(t,r);else if(Kg(s,t,e,n,r))r.stopPropagation();else if(_c(t,r),e&4&&-1<Wg.indexOf(t)){for(;s!==null;){var i=ks(s);if(i!==null&&Kd(i),i=zo(t,e,n,r),i===null&&qa(t,e,r,bi,n),i===s)break;s=i}s!==null&&r.stopPropagation()}else qa(t,e,r,null,n)}}var bi=null;function zo(t,e,n,r){if(bi=null,t=zl(r),t=hn(t),t!==null)if(e=Rn(t),e===null)t=null;else if(n=e.tag,n===13){if(t=jd(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null);return bi=t,null}function Yd(t){switch(t){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Fg()){case Ul:return 1;case Bd:return 4;case Oi:case Mg:return 16;case Vd:return 536870912;default:return 16}default:return 16}}var Bt=null,Wl=null,di=null;function qd(){if(di)return di;var t,e=Wl,n=e.length,r,s="value"in Bt?Bt.value:Bt.textContent,i=s.length;for(t=0;t<n&&e[t]===s[t];t++);var a=n-t;for(r=1;r<=a&&e[n-r]===s[i-r];r++);return di=s.slice(t,1<r?1-r:void 0)}function hi(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Us(){return!0}function kc(){return!1}function Ve(t){function e(n,r,s,i,a){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=i,this.target=a,this.currentTarget=null;for(var o in t)t.hasOwnProperty(o)&&(n=t[o],this[o]=n?n(i):i[o]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Us:kc,this.isPropagationStopped=kc,this}return q(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Us)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Us)},persist:function(){},isPersistent:Us}),e}var pr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Kl=Ve(pr),Ss=q({},pr,{view:0,detail:0}),Gg=Ve(Ss),Va,Ha,Cr,oa=q({},Ss,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ql,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Cr&&(Cr&&t.type==="mousemove"?(Va=t.screenX-Cr.screenX,Ha=t.screenY-Cr.screenY):Ha=Va=0,Cr=t),Va)},movementY:function(t){return"movementY"in t?t.movementY:Ha}}),Cc=Ve(oa),Yg=q({},oa,{dataTransfer:0}),qg=Ve(Yg),Zg=q({},Ss,{relatedTarget:0}),Wa=Ve(Zg),ey=q({},pr,{animationName:0,elapsedTime:0,pseudoElement:0}),ty=Ve(ey),ny=q({},pr,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),ry=Ve(ny),sy=q({},pr,{data:0}),Ec=Ve(sy),iy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},ay={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},oy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ly(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=oy[t])?!!e[t]:!1}function Ql(){return ly}var uy=q({},Ss,{key:function(t){if(t.key){var e=iy[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=hi(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?ay[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ql,charCode:function(t){return t.type==="keypress"?hi(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?hi(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),cy=Ve(uy),fy=q({},oa,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Pc=Ve(fy),dy=q({},Ss,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ql}),hy=Ve(dy),py=q({},pr,{propertyName:0,elapsedTime:0,pseudoElement:0}),my=Ve(py),gy=q({},oa,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),yy=Ve(gy),vy=[9,13,27,32],Jl=Nt&&"CompositionEvent"in window,Vr=null;Nt&&"documentMode"in document&&(Vr=document.documentMode);var wy=Nt&&"TextEvent"in window&&!Vr,Zd=Nt&&(!Jl||Vr&&8<Vr&&11>=Vr),Rc=" ",Nc=!1;function eh(t,e){switch(t){case"keyup":return vy.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function th(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var zn=!1;function xy(t,e){switch(t){case"compositionend":return th(e);case"keypress":return e.which!==32?null:(Nc=!0,Rc);case"textInput":return t=e.data,t===Rc&&Nc?null:t;default:return null}}function _y(t,e){if(zn)return t==="compositionend"||!Jl&&eh(t,e)?(t=qd(),di=Wl=Bt=null,zn=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Zd&&e.locale!=="ko"?null:e.data;default:return null}}var Sy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Lc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Sy[t.type]:e==="textarea"}function nh(t,e,n,r){bd(r),e=Ti(e,"onChange"),0<e.length&&(n=new Kl("onChange","change",null,n,r),t.push({event:n,listeners:e}))}var Hr=null,is=null;function ky(t){hh(t,0)}function la(t){var e=Vn(t);if(Pd(e))return t}function Cy(t,e){if(t==="change")return e}var rh=!1;if(Nt){var Ka;if(Nt){var Qa="oninput"in document;if(!Qa){var Oc=document.createElement("div");Oc.setAttribute("oninput","return;"),Qa=typeof Oc.oninput=="function"}Ka=Qa}else Ka=!1;rh=Ka&&(!document.documentMode||9<document.documentMode)}function Ac(){Hr&&(Hr.detachEvent("onpropertychange",sh),is=Hr=null)}function sh(t){if(t.propertyName==="value"&&la(is)){var e=[];nh(e,is,t,zl(t)),Md(ky,e)}}function Ey(t,e,n){t==="focusin"?(Ac(),Hr=e,is=n,Hr.attachEvent("onpropertychange",sh)):t==="focusout"&&Ac()}function Py(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return la(is)}function Ry(t,e){if(t==="click")return la(e)}function Ny(t,e){if(t==="input"||t==="change")return la(e)}function Ly(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var ot=typeof Object.is=="function"?Object.is:Ly;function as(t,e){if(ot(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),r=Object.keys(e);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!So.call(e,s)||!ot(t[s],e[s]))return!1}return!0}function Ic(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function bc(t,e){var n=Ic(t);t=0;for(var r;n;){if(n.nodeType===3){if(r=t+n.textContent.length,t<=e&&r>=e)return{node:n,offset:e-t};t=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ic(n)}}function ih(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?ih(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function ah(){for(var t=window,e=Ri();e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=Ri(t.document)}return e}function Xl(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}function Oy(t){var e=ah(),n=t.focusedElem,r=t.selectionRange;if(e!==n&&n&&n.ownerDocument&&ih(n.ownerDocument.documentElement,n)){if(r!==null&&Xl(n)){if(e=r.start,t=r.end,t===void 0&&(t=e),"selectionStart"in n)n.selectionStart=e,n.selectionEnd=Math.min(t,n.value.length);else if(t=(e=n.ownerDocument||document)&&e.defaultView||window,t.getSelection){t=t.getSelection();var s=n.textContent.length,i=Math.min(r.start,s);r=r.end===void 0?i:Math.min(r.end,s),!t.extend&&i>r&&(s=r,r=i,i=s),s=bc(n,i);var a=bc(n,r);s&&a&&(t.rangeCount!==1||t.anchorNode!==s.node||t.anchorOffset!==s.offset||t.focusNode!==a.node||t.focusOffset!==a.offset)&&(e=e.createRange(),e.setStart(s.node,s.offset),t.removeAllRanges(),i>r?(t.addRange(e),t.extend(a.node,a.offset)):(e.setEnd(a.node,a.offset),t.addRange(e)))}}for(e=[],t=n;t=t.parentNode;)t.nodeType===1&&e.push({element:t,left:t.scrollLeft,top:t.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<e.length;n++)t=e[n],t.element.scrollLeft=t.left,t.element.scrollTop=t.top}}var Ay=Nt&&"documentMode"in document&&11>=document.documentMode,Un=null,Uo=null,Wr=null,Bo=!1;function Tc(t,e,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Bo||Un==null||Un!==Ri(r)||(r=Un,"selectionStart"in r&&Xl(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Wr&&as(Wr,r)||(Wr=r,r=Ti(Uo,"onSelect"),0<r.length&&(e=new Kl("onSelect","select",null,e,n),t.push({event:e,listeners:r}),e.target=Un)))}function Bs(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Bn={animationend:Bs("Animation","AnimationEnd"),animationiteration:Bs("Animation","AnimationIteration"),animationstart:Bs("Animation","AnimationStart"),transitionend:Bs("Transition","TransitionEnd")},Ja={},oh={};Nt&&(oh=document.createElement("div").style,"AnimationEvent"in window||(delete Bn.animationend.animation,delete Bn.animationiteration.animation,delete Bn.animationstart.animation),"TransitionEvent"in window||delete Bn.transitionend.transition);function ua(t){if(Ja[t])return Ja[t];if(!Bn[t])return t;var e=Bn[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in oh)return Ja[t]=e[n];return t}var lh=ua("animationend"),uh=ua("animationiteration"),ch=ua("animationstart"),fh=ua("transitionend"),dh=new Map,$c="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function tn(t,e){dh.set(t,e),Pn(e,[t])}for(var Xa=0;Xa<$c.length;Xa++){var Ga=$c[Xa],Iy=Ga.toLowerCase(),by=Ga[0].toUpperCase()+Ga.slice(1);tn(Iy,"on"+by)}tn(lh,"onAnimationEnd");tn(uh,"onAnimationIteration");tn(ch,"onAnimationStart");tn("dblclick","onDoubleClick");tn("focusin","onFocus");tn("focusout","onBlur");tn(fh,"onTransitionEnd");ar("onMouseEnter",["mouseout","mouseover"]);ar("onMouseLeave",["mouseout","mouseover"]);ar("onPointerEnter",["pointerout","pointerover"]);ar("onPointerLeave",["pointerout","pointerover"]);Pn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Pn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Pn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Pn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Pn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Pn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Tr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ty=new Set("cancel close invalid load scroll toggle".split(" ").concat(Tr));function Fc(t,e,n){var r=t.type||"unknown-event";t.currentTarget=n,Ig(r,e,void 0,t),t.currentTarget=null}function hh(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var r=t[n],s=r.event;r=r.listeners;e:{var i=void 0;if(e)for(var a=r.length-1;0<=a;a--){var o=r[a],l=o.instance,u=o.currentTarget;if(o=o.listener,l!==i&&s.isPropagationStopped())break e;Fc(s,o,u),i=l}else for(a=0;a<r.length;a++){if(o=r[a],l=o.instance,u=o.currentTarget,o=o.listener,l!==i&&s.isPropagationStopped())break e;Fc(s,o,u),i=l}}}if(Li)throw t=Mo,Li=!1,Mo=null,t}function W(t,e){var n=e[Qo];n===void 0&&(n=e[Qo]=new Set);var r=t+"__bubble";n.has(r)||(ph(e,t,2,!1),n.add(r))}function Ya(t,e,n){var r=0;e&&(r|=4),ph(n,t,r,e)}var Vs="_reactListening"+Math.random().toString(36).slice(2);function os(t){if(!t[Vs]){t[Vs]=!0,_d.forEach(function(n){n!=="selectionchange"&&(Ty.has(n)||Ya(n,!1,t),Ya(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Vs]||(e[Vs]=!0,Ya("selectionchange",!1,e))}}function ph(t,e,n,r){switch(Yd(e)){case 1:var s=Jg;break;case 4:s=Xg;break;default:s=Hl}n=s.bind(null,e,n,t),s=void 0,!Fo||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(s=!0),r?s!==void 0?t.addEventListener(e,n,{capture:!0,passive:s}):t.addEventListener(e,n,!0):s!==void 0?t.addEventListener(e,n,{passive:s}):t.addEventListener(e,n,!1)}function qa(t,e,n,r,s){var i=r;if(!(e&1)&&!(e&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var o=r.stateNode.containerInfo;if(o===s||o.nodeType===8&&o.parentNode===s)break;if(a===4)for(a=r.return;a!==null;){var l=a.tag;if((l===3||l===4)&&(l=a.stateNode.containerInfo,l===s||l.nodeType===8&&l.parentNode===s))return;a=a.return}for(;o!==null;){if(a=hn(o),a===null)return;if(l=a.tag,l===5||l===6){r=i=a;continue e}o=o.parentNode}}r=r.return}Md(function(){var u=i,h=zl(n),d=[];e:{var c=dh.get(t);if(c!==void 0){var g=Kl,v=t;switch(t){case"keypress":if(hi(n)===0)break e;case"keydown":case"keyup":g=cy;break;case"focusin":v="focus",g=Wa;break;case"focusout":v="blur",g=Wa;break;case"beforeblur":case"afterblur":g=Wa;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=Cc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=qg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=hy;break;case lh:case uh:case ch:g=ty;break;case fh:g=my;break;case"scroll":g=Gg;break;case"wheel":g=yy;break;case"copy":case"cut":case"paste":g=ry;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=Pc}var w=(e&4)!==0,_=!w&&t==="scroll",m=w?c!==null?c+"Capture":null:c;w=[];for(var f=u,p;f!==null;){p=f;var y=p.stateNode;if(p.tag===5&&y!==null&&(p=y,m!==null&&(y=ts(f,m),y!=null&&w.push(ls(f,y,p)))),_)break;f=f.return}0<w.length&&(c=new g(c,v,null,n,h),d.push({event:c,listeners:w}))}}if(!(e&7)){e:{if(c=t==="mouseover"||t==="pointerover",g=t==="mouseout"||t==="pointerout",c&&n!==To&&(v=n.relatedTarget||n.fromElement)&&(hn(v)||v[Lt]))break e;if((g||c)&&(c=h.window===h?h:(c=h.ownerDocument)?c.defaultView||c.parentWindow:window,g?(v=n.relatedTarget||n.toElement,g=u,v=v?hn(v):null,v!==null&&(_=Rn(v),v!==_||v.tag!==5&&v.tag!==6)&&(v=null)):(g=null,v=u),g!==v)){if(w=Cc,y="onMouseLeave",m="onMouseEnter",f="mouse",(t==="pointerout"||t==="pointerover")&&(w=Pc,y="onPointerLeave",m="onPointerEnter",f="pointer"),_=g==null?c:Vn(g),p=v==null?c:Vn(v),c=new w(y,f+"leave",g,n,h),c.target=_,c.relatedTarget=p,y=null,hn(h)===u&&(w=new w(m,f+"enter",v,n,h),w.target=p,w.relatedTarget=_,y=w),_=y,g&&v)t:{for(w=g,m=v,f=0,p=w;p;p=Ln(p))f++;for(p=0,y=m;y;y=Ln(y))p++;for(;0<f-p;)w=Ln(w),f--;for(;0<p-f;)m=Ln(m),p--;for(;f--;){if(w===m||m!==null&&w===m.alternate)break t;w=Ln(w),m=Ln(m)}w=null}else w=null;g!==null&&Mc(d,c,g,w,!1),v!==null&&_!==null&&Mc(d,_,v,w,!0)}}e:{if(c=u?Vn(u):window,g=c.nodeName&&c.nodeName.toLowerCase(),g==="select"||g==="input"&&c.type==="file")var k=Cy;else if(Lc(c))if(rh)k=Ny;else{k=Py;var S=Ey}else(g=c.nodeName)&&g.toLowerCase()==="input"&&(c.type==="checkbox"||c.type==="radio")&&(k=Ry);if(k&&(k=k(t,u))){nh(d,k,n,h);break e}S&&S(t,c,u),t==="focusout"&&(S=c._wrapperState)&&S.controlled&&c.type==="number"&&Lo(c,"number",c.value)}switch(S=u?Vn(u):window,t){case"focusin":(Lc(S)||S.contentEditable==="true")&&(Un=S,Uo=u,Wr=null);break;case"focusout":Wr=Uo=Un=null;break;case"mousedown":Bo=!0;break;case"contextmenu":case"mouseup":case"dragend":Bo=!1,Tc(d,n,h);break;case"selectionchange":if(Ay)break;case"keydown":case"keyup":Tc(d,n,h)}var x;if(Jl)e:{switch(t){case"compositionstart":var R="onCompositionStart";break e;case"compositionend":R="onCompositionEnd";break e;case"compositionupdate":R="onCompositionUpdate";break e}R=void 0}else zn?eh(t,n)&&(R="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(R="onCompositionStart");R&&(Zd&&n.locale!=="ko"&&(zn||R!=="onCompositionStart"?R==="onCompositionEnd"&&zn&&(x=qd()):(Bt=h,Wl="value"in Bt?Bt.value:Bt.textContent,zn=!0)),S=Ti(u,R),0<S.length&&(R=new Ec(R,t,null,n,h),d.push({event:R,listeners:S}),x?R.data=x:(x=th(n),x!==null&&(R.data=x)))),(x=wy?xy(t,n):_y(t,n))&&(u=Ti(u,"onBeforeInput"),0<u.length&&(h=new Ec("onBeforeInput","beforeinput",null,n,h),d.push({event:h,listeners:u}),h.data=x))}hh(d,e)})}function ls(t,e,n){return{instance:t,listener:e,currentTarget:n}}function Ti(t,e){for(var n=e+"Capture",r=[];t!==null;){var s=t,i=s.stateNode;s.tag===5&&i!==null&&(s=i,i=ts(t,n),i!=null&&r.unshift(ls(t,i,s)),i=ts(t,e),i!=null&&r.push(ls(t,i,s))),t=t.return}return r}function Ln(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5);return t||null}function Mc(t,e,n,r,s){for(var i=e._reactName,a=[];n!==null&&n!==r;){var o=n,l=o.alternate,u=o.stateNode;if(l!==null&&l===r)break;o.tag===5&&u!==null&&(o=u,s?(l=ts(n,i),l!=null&&a.unshift(ls(n,l,o))):s||(l=ts(n,i),l!=null&&a.push(ls(n,l,o)))),n=n.return}a.length!==0&&t.push({event:e,listeners:a})}var $y=/\r\n?/g,Fy=/\u0000|\uFFFD/g;function jc(t){return(typeof t=="string"?t:""+t).replace($y,`
`).replace(Fy,"")}function Hs(t,e,n){if(e=jc(e),jc(t)!==e&&n)throw Error(E(425))}function $i(){}var Vo=null,Ho=null;function Wo(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var Ko=typeof setTimeout=="function"?setTimeout:void 0,My=typeof clearTimeout=="function"?clearTimeout:void 0,Dc=typeof Promise=="function"?Promise:void 0,jy=typeof queueMicrotask=="function"?queueMicrotask:typeof Dc<"u"?function(t){return Dc.resolve(null).then(t).catch(Dy)}:Ko;function Dy(t){setTimeout(function(){throw t})}function Za(t,e){var n=e,r=0;do{var s=n.nextSibling;if(t.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){t.removeChild(s),ss(e);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);ss(e)}function Qt(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?")break;if(e==="/$")return null}}return t}function zc(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}var mr=Math.random().toString(36).slice(2),gt="__reactFiber$"+mr,us="__reactProps$"+mr,Lt="__reactContainer$"+mr,Qo="__reactEvents$"+mr,zy="__reactListeners$"+mr,Uy="__reactHandles$"+mr;function hn(t){var e=t[gt];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Lt]||n[gt]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=zc(t);t!==null;){if(n=t[gt])return n;t=zc(t)}return e}t=n,n=t.parentNode}return null}function ks(t){return t=t[gt]||t[Lt],!t||t.tag!==5&&t.tag!==6&&t.tag!==13&&t.tag!==3?null:t}function Vn(t){if(t.tag===5||t.tag===6)return t.stateNode;throw Error(E(33))}function ca(t){return t[us]||null}var Jo=[],Hn=-1;function nn(t){return{current:t}}function K(t){0>Hn||(t.current=Jo[Hn],Jo[Hn]=null,Hn--)}function H(t,e){Hn++,Jo[Hn]=t.current,t.current=e}var en={},Se=nn(en),Ae=nn(!1),xn=en;function or(t,e){var n=t.type.contextTypes;if(!n)return en;var r=t.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===e)return r.__reactInternalMemoizedMaskedChildContext;var s={},i;for(i in n)s[i]=e[i];return r&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=e,t.__reactInternalMemoizedMaskedChildContext=s),s}function Ie(t){return t=t.childContextTypes,t!=null}function Fi(){K(Ae),K(Se)}function Uc(t,e,n){if(Se.current!==en)throw Error(E(168));H(Se,e),H(Ae,n)}function mh(t,e,n){var r=t.stateNode;if(e=e.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in e))throw Error(E(108,Eg(t)||"Unknown",s));return q({},n,r)}function Mi(t){return t=(t=t.stateNode)&&t.__reactInternalMemoizedMergedChildContext||en,xn=Se.current,H(Se,t),H(Ae,Ae.current),!0}function Bc(t,e,n){var r=t.stateNode;if(!r)throw Error(E(169));n?(t=mh(t,e,xn),r.__reactInternalMemoizedMergedChildContext=t,K(Ae),K(Se),H(Se,t)):K(Ae),H(Ae,n)}var Ct=null,fa=!1,eo=!1;function gh(t){Ct===null?Ct=[t]:Ct.push(t)}function By(t){fa=!0,gh(t)}function rn(){if(!eo&&Ct!==null){eo=!0;var t=0,e=U;try{var n=Ct;for(U=1;t<n.length;t++){var r=n[t];do r=r(!0);while(r!==null)}Ct=null,fa=!1}catch(s){throw Ct!==null&&(Ct=Ct.slice(t+1)),Ud(Ul,rn),s}finally{U=e,eo=!1}}return null}var Wn=[],Kn=0,ji=null,Di=0,He=[],We=0,_n=null,Et=1,Pt="";function cn(t,e){Wn[Kn++]=Di,Wn[Kn++]=ji,ji=t,Di=e}function yh(t,e,n){He[We++]=Et,He[We++]=Pt,He[We++]=_n,_n=t;var r=Et;t=Pt;var s=32-it(r)-1;r&=~(1<<s),n+=1;var i=32-it(e)+s;if(30<i){var a=s-s%5;i=(r&(1<<a)-1).toString(32),r>>=a,s-=a,Et=1<<32-it(e)+s|n<<s|r,Pt=i+t}else Et=1<<i|n<<s|r,Pt=t}function Gl(t){t.return!==null&&(cn(t,1),yh(t,1,0))}function Yl(t){for(;t===ji;)ji=Wn[--Kn],Wn[Kn]=null,Di=Wn[--Kn],Wn[Kn]=null;for(;t===_n;)_n=He[--We],He[We]=null,Pt=He[--We],He[We]=null,Et=He[--We],He[We]=null}var ze=null,De=null,J=!1,rt=null;function vh(t,e){var n=Ke(5,null,null,0);n.elementType="DELETED",n.stateNode=e,n.return=t,e=t.deletions,e===null?(t.deletions=[n],t.flags|=16):e.push(n)}function Vc(t,e){switch(t.tag){case 5:var n=t.type;return e=e.nodeType!==1||n.toLowerCase()!==e.nodeName.toLowerCase()?null:e,e!==null?(t.stateNode=e,ze=t,De=Qt(e.firstChild),!0):!1;case 6:return e=t.pendingProps===""||e.nodeType!==3?null:e,e!==null?(t.stateNode=e,ze=t,De=null,!0):!1;case 13:return e=e.nodeType!==8?null:e,e!==null?(n=_n!==null?{id:Et,overflow:Pt}:null,t.memoizedState={dehydrated:e,treeContext:n,retryLane:1073741824},n=Ke(18,null,null,0),n.stateNode=e,n.return=t,t.child=n,ze=t,De=null,!0):!1;default:return!1}}function Xo(t){return(t.mode&1)!==0&&(t.flags&128)===0}function Go(t){if(J){var e=De;if(e){var n=e;if(!Vc(t,e)){if(Xo(t))throw Error(E(418));e=Qt(n.nextSibling);var r=ze;e&&Vc(t,e)?vh(r,n):(t.flags=t.flags&-4097|2,J=!1,ze=t)}}else{if(Xo(t))throw Error(E(418));t.flags=t.flags&-4097|2,J=!1,ze=t}}}function Hc(t){for(t=t.return;t!==null&&t.tag!==5&&t.tag!==3&&t.tag!==13;)t=t.return;ze=t}function Ws(t){if(t!==ze)return!1;if(!J)return Hc(t),J=!0,!1;var e;if((e=t.tag!==3)&&!(e=t.tag!==5)&&(e=t.type,e=e!=="head"&&e!=="body"&&!Wo(t.type,t.memoizedProps)),e&&(e=De)){if(Xo(t))throw wh(),Error(E(418));for(;e;)vh(t,e),e=Qt(e.nextSibling)}if(Hc(t),t.tag===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(E(317));e:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="/$"){if(e===0){De=Qt(t.nextSibling);break e}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++}t=t.nextSibling}De=null}}else De=ze?Qt(t.stateNode.nextSibling):null;return!0}function wh(){for(var t=De;t;)t=Qt(t.nextSibling)}function lr(){De=ze=null,J=!1}function ql(t){rt===null?rt=[t]:rt.push(t)}var Vy=It.ReactCurrentBatchConfig;function Er(t,e,n){if(t=n.ref,t!==null&&typeof t!="function"&&typeof t!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(E(309));var r=n.stateNode}if(!r)throw Error(E(147,t));var s=r,i=""+t;return e!==null&&e.ref!==null&&typeof e.ref=="function"&&e.ref._stringRef===i?e.ref:(e=function(a){var o=s.refs;a===null?delete o[i]:o[i]=a},e._stringRef=i,e)}if(typeof t!="string")throw Error(E(284));if(!n._owner)throw Error(E(290,t))}return t}function Ks(t,e){throw t=Object.prototype.toString.call(e),Error(E(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t))}function Wc(t){var e=t._init;return e(t._payload)}function xh(t){function e(m,f){if(t){var p=m.deletions;p===null?(m.deletions=[f],m.flags|=16):p.push(f)}}function n(m,f){if(!t)return null;for(;f!==null;)e(m,f),f=f.sibling;return null}function r(m,f){for(m=new Map;f!==null;)f.key!==null?m.set(f.key,f):m.set(f.index,f),f=f.sibling;return m}function s(m,f){return m=Yt(m,f),m.index=0,m.sibling=null,m}function i(m,f,p){return m.index=p,t?(p=m.alternate,p!==null?(p=p.index,p<f?(m.flags|=2,f):p):(m.flags|=2,f)):(m.flags|=1048576,f)}function a(m){return t&&m.alternate===null&&(m.flags|=2),m}function o(m,f,p,y){return f===null||f.tag!==6?(f=oo(p,m.mode,y),f.return=m,f):(f=s(f,p),f.return=m,f)}function l(m,f,p,y){var k=p.type;return k===Dn?h(m,f,p.props.children,y,p.key):f!==null&&(f.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===jt&&Wc(k)===f.type)?(y=s(f,p.props),y.ref=Er(m,f,p),y.return=m,y):(y=xi(p.type,p.key,p.props,null,m.mode,y),y.ref=Er(m,f,p),y.return=m,y)}function u(m,f,p,y){return f===null||f.tag!==4||f.stateNode.containerInfo!==p.containerInfo||f.stateNode.implementation!==p.implementation?(f=lo(p,m.mode,y),f.return=m,f):(f=s(f,p.children||[]),f.return=m,f)}function h(m,f,p,y,k){return f===null||f.tag!==7?(f=vn(p,m.mode,y,k),f.return=m,f):(f=s(f,p),f.return=m,f)}function d(m,f,p){if(typeof f=="string"&&f!==""||typeof f=="number")return f=oo(""+f,m.mode,p),f.return=m,f;if(typeof f=="object"&&f!==null){switch(f.$$typeof){case $s:return p=xi(f.type,f.key,f.props,null,m.mode,p),p.ref=Er(m,null,f),p.return=m,p;case jn:return f=lo(f,m.mode,p),f.return=m,f;case jt:var y=f._init;return d(m,y(f._payload),p)}if(Ir(f)||xr(f))return f=vn(f,m.mode,p,null),f.return=m,f;Ks(m,f)}return null}function c(m,f,p,y){var k=f!==null?f.key:null;if(typeof p=="string"&&p!==""||typeof p=="number")return k!==null?null:o(m,f,""+p,y);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case $s:return p.key===k?l(m,f,p,y):null;case jn:return p.key===k?u(m,f,p,y):null;case jt:return k=p._init,c(m,f,k(p._payload),y)}if(Ir(p)||xr(p))return k!==null?null:h(m,f,p,y,null);Ks(m,p)}return null}function g(m,f,p,y,k){if(typeof y=="string"&&y!==""||typeof y=="number")return m=m.get(p)||null,o(f,m,""+y,k);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case $s:return m=m.get(y.key===null?p:y.key)||null,l(f,m,y,k);case jn:return m=m.get(y.key===null?p:y.key)||null,u(f,m,y,k);case jt:var S=y._init;return g(m,f,p,S(y._payload),k)}if(Ir(y)||xr(y))return m=m.get(p)||null,h(f,m,y,k,null);Ks(f,y)}return null}function v(m,f,p,y){for(var k=null,S=null,x=f,R=f=0,F=null;x!==null&&R<p.length;R++){x.index>R?(F=x,x=null):F=x.sibling;var L=c(m,x,p[R],y);if(L===null){x===null&&(x=F);break}t&&x&&L.alternate===null&&e(m,x),f=i(L,f,R),S===null?k=L:S.sibling=L,S=L,x=F}if(R===p.length)return n(m,x),J&&cn(m,R),k;if(x===null){for(;R<p.length;R++)x=d(m,p[R],y),x!==null&&(f=i(x,f,R),S===null?k=x:S.sibling=x,S=x);return J&&cn(m,R),k}for(x=r(m,x);R<p.length;R++)F=g(x,m,R,p[R],y),F!==null&&(t&&F.alternate!==null&&x.delete(F.key===null?R:F.key),f=i(F,f,R),S===null?k=F:S.sibling=F,S=F);return t&&x.forEach(function(T){return e(m,T)}),J&&cn(m,R),k}function w(m,f,p,y){var k=xr(p);if(typeof k!="function")throw Error(E(150));if(p=k.call(p),p==null)throw Error(E(151));for(var S=k=null,x=f,R=f=0,F=null,L=p.next();x!==null&&!L.done;R++,L=p.next()){x.index>R?(F=x,x=null):F=x.sibling;var T=c(m,x,L.value,y);if(T===null){x===null&&(x=F);break}t&&x&&T.alternate===null&&e(m,x),f=i(T,f,R),S===null?k=T:S.sibling=T,S=T,x=F}if(L.done)return n(m,x),J&&cn(m,R),k;if(x===null){for(;!L.done;R++,L=p.next())L=d(m,L.value,y),L!==null&&(f=i(L,f,R),S===null?k=L:S.sibling=L,S=L);return J&&cn(m,R),k}for(x=r(m,x);!L.done;R++,L=p.next())L=g(x,m,R,L.value,y),L!==null&&(t&&L.alternate!==null&&x.delete(L.key===null?R:L.key),f=i(L,f,R),S===null?k=L:S.sibling=L,S=L);return t&&x.forEach(function(Te){return e(m,Te)}),J&&cn(m,R),k}function _(m,f,p,y){if(typeof p=="object"&&p!==null&&p.type===Dn&&p.key===null&&(p=p.props.children),typeof p=="object"&&p!==null){switch(p.$$typeof){case $s:e:{for(var k=p.key,S=f;S!==null;){if(S.key===k){if(k=p.type,k===Dn){if(S.tag===7){n(m,S.sibling),f=s(S,p.props.children),f.return=m,m=f;break e}}else if(S.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===jt&&Wc(k)===S.type){n(m,S.sibling),f=s(S,p.props),f.ref=Er(m,S,p),f.return=m,m=f;break e}n(m,S);break}else e(m,S);S=S.sibling}p.type===Dn?(f=vn(p.props.children,m.mode,y,p.key),f.return=m,m=f):(y=xi(p.type,p.key,p.props,null,m.mode,y),y.ref=Er(m,f,p),y.return=m,m=y)}return a(m);case jn:e:{for(S=p.key;f!==null;){if(f.key===S)if(f.tag===4&&f.stateNode.containerInfo===p.containerInfo&&f.stateNode.implementation===p.implementation){n(m,f.sibling),f=s(f,p.children||[]),f.return=m,m=f;break e}else{n(m,f);break}else e(m,f);f=f.sibling}f=lo(p,m.mode,y),f.return=m,m=f}return a(m);case jt:return S=p._init,_(m,f,S(p._payload),y)}if(Ir(p))return v(m,f,p,y);if(xr(p))return w(m,f,p,y);Ks(m,p)}return typeof p=="string"&&p!==""||typeof p=="number"?(p=""+p,f!==null&&f.tag===6?(n(m,f.sibling),f=s(f,p),f.return=m,m=f):(n(m,f),f=oo(p,m.mode,y),f.return=m,m=f),a(m)):n(m,f)}return _}var ur=xh(!0),_h=xh(!1),zi=nn(null),Ui=null,Qn=null,Zl=null;function eu(){Zl=Qn=Ui=null}function tu(t){var e=zi.current;K(zi),t._currentValue=e}function Yo(t,e,n){for(;t!==null;){var r=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,r!==null&&(r.childLanes|=e)):r!==null&&(r.childLanes&e)!==e&&(r.childLanes|=e),t===n)break;t=t.return}}function tr(t,e){Ui=t,Zl=Qn=null,t=t.dependencies,t!==null&&t.firstContext!==null&&(t.lanes&e&&(Oe=!0),t.firstContext=null)}function Xe(t){var e=t._currentValue;if(Zl!==t)if(t={context:t,memoizedValue:e,next:null},Qn===null){if(Ui===null)throw Error(E(308));Qn=t,Ui.dependencies={lanes:0,firstContext:t}}else Qn=Qn.next=t;return e}var pn=null;function nu(t){pn===null?pn=[t]:pn.push(t)}function Sh(t,e,n,r){var s=e.interleaved;return s===null?(n.next=n,nu(e)):(n.next=s.next,s.next=n),e.interleaved=n,Ot(t,r)}function Ot(t,e){t.lanes|=e;var n=t.alternate;for(n!==null&&(n.lanes|=e),n=t,t=t.return;t!==null;)t.childLanes|=e,n=t.alternate,n!==null&&(n.childLanes|=e),n=t,t=t.return;return n.tag===3?n.stateNode:null}var Dt=!1;function ru(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function kh(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,effects:t.effects})}function Rt(t,e){return{eventTime:t,lane:e,tag:0,payload:null,callback:null,next:null}}function Jt(t,e,n){var r=t.updateQueue;if(r===null)return null;if(r=r.shared,z&2){var s=r.pending;return s===null?e.next=e:(e.next=s.next,s.next=e),r.pending=e,Ot(t,n)}return s=r.interleaved,s===null?(e.next=e,nu(r)):(e.next=s.next,s.next=e),r.interleaved=e,Ot(t,n)}function pi(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194240)!==0)){var r=e.lanes;r&=t.pendingLanes,n|=r,e.lanes=n,Bl(t,n)}}function Kc(t,e){var n=t.updateQueue,r=t.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?s=i=a:i=i.next=a,n=n.next}while(n!==null);i===null?s=i=e:i=i.next=e}else s=i=e;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:i,shared:r.shared,effects:r.effects},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}function Bi(t,e,n,r){var s=t.updateQueue;Dt=!1;var i=s.firstBaseUpdate,a=s.lastBaseUpdate,o=s.shared.pending;if(o!==null){s.shared.pending=null;var l=o,u=l.next;l.next=null,a===null?i=u:a.next=u,a=l;var h=t.alternate;h!==null&&(h=h.updateQueue,o=h.lastBaseUpdate,o!==a&&(o===null?h.firstBaseUpdate=u:o.next=u,h.lastBaseUpdate=l))}if(i!==null){var d=s.baseState;a=0,h=u=l=null,o=i;do{var c=o.lane,g=o.eventTime;if((r&c)===c){h!==null&&(h=h.next={eventTime:g,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var v=t,w=o;switch(c=e,g=n,w.tag){case 1:if(v=w.payload,typeof v=="function"){d=v.call(g,d,c);break e}d=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=w.payload,c=typeof v=="function"?v.call(g,d,c):v,c==null)break e;d=q({},d,c);break e;case 2:Dt=!0}}o.callback!==null&&o.lane!==0&&(t.flags|=64,c=s.effects,c===null?s.effects=[o]:c.push(o))}else g={eventTime:g,lane:c,tag:o.tag,payload:o.payload,callback:o.callback,next:null},h===null?(u=h=g,l=d):h=h.next=g,a|=c;if(o=o.next,o===null){if(o=s.shared.pending,o===null)break;c=o,o=c.next,c.next=null,s.lastBaseUpdate=c,s.shared.pending=null}}while(!0);if(h===null&&(l=d),s.baseState=l,s.firstBaseUpdate=u,s.lastBaseUpdate=h,e=s.shared.interleaved,e!==null){s=e;do a|=s.lane,s=s.next;while(s!==e)}else i===null&&(s.shared.lanes=0);kn|=a,t.lanes=a,t.memoizedState=d}}function Qc(t,e,n){if(t=e.effects,e.effects=null,t!==null)for(e=0;e<t.length;e++){var r=t[e],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(E(191,s));s.call(r)}}}var Cs={},xt=nn(Cs),cs=nn(Cs),fs=nn(Cs);function mn(t){if(t===Cs)throw Error(E(174));return t}function su(t,e){switch(H(fs,e),H(cs,t),H(xt,Cs),t=e.nodeType,t){case 9:case 11:e=(e=e.documentElement)?e.namespaceURI:Ao(null,"");break;default:t=t===8?e.parentNode:e,e=t.namespaceURI||null,t=t.tagName,e=Ao(e,t)}K(xt),H(xt,e)}function cr(){K(xt),K(cs),K(fs)}function Ch(t){mn(fs.current);var e=mn(xt.current),n=Ao(e,t.type);e!==n&&(H(cs,t),H(xt,n))}function iu(t){cs.current===t&&(K(xt),K(cs))}var G=nn(0);function Vi(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if(e.flags&128)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}var to=[];function au(){for(var t=0;t<to.length;t++)to[t]._workInProgressVersionPrimary=null;to.length=0}var mi=It.ReactCurrentDispatcher,no=It.ReactCurrentBatchConfig,Sn=0,Y=null,ie=null,ce=null,Hi=!1,Kr=!1,ds=0,Hy=0;function ve(){throw Error(E(321))}function ou(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!ot(t[n],e[n]))return!1;return!0}function lu(t,e,n,r,s,i){if(Sn=i,Y=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,mi.current=t===null||t.memoizedState===null?Jy:Xy,t=n(r,s),Kr){i=0;do{if(Kr=!1,ds=0,25<=i)throw Error(E(301));i+=1,ce=ie=null,e.updateQueue=null,mi.current=Gy,t=n(r,s)}while(Kr)}if(mi.current=Wi,e=ie!==null&&ie.next!==null,Sn=0,ce=ie=Y=null,Hi=!1,e)throw Error(E(300));return t}function uu(){var t=ds!==0;return ds=0,t}function ht(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ce===null?Y.memoizedState=ce=t:ce=ce.next=t,ce}function Ge(){if(ie===null){var t=Y.alternate;t=t!==null?t.memoizedState:null}else t=ie.next;var e=ce===null?Y.memoizedState:ce.next;if(e!==null)ce=e,ie=t;else{if(t===null)throw Error(E(310));ie=t,t={memoizedState:ie.memoizedState,baseState:ie.baseState,baseQueue:ie.baseQueue,queue:ie.queue,next:null},ce===null?Y.memoizedState=ce=t:ce=ce.next=t}return ce}function hs(t,e){return typeof e=="function"?e(t):e}function ro(t){var e=Ge(),n=e.queue;if(n===null)throw Error(E(311));n.lastRenderedReducer=t;var r=ie,s=r.baseQueue,i=n.pending;if(i!==null){if(s!==null){var a=s.next;s.next=i.next,i.next=a}r.baseQueue=s=i,n.pending=null}if(s!==null){i=s.next,r=r.baseState;var o=a=null,l=null,u=i;do{var h=u.lane;if((Sn&h)===h)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:t(r,u.action);else{var d={lane:h,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(o=l=d,a=r):l=l.next=d,Y.lanes|=h,kn|=h}u=u.next}while(u!==null&&u!==i);l===null?a=r:l.next=o,ot(r,e.memoizedState)||(Oe=!0),e.memoizedState=r,e.baseState=a,e.baseQueue=l,n.lastRenderedState=r}if(t=n.interleaved,t!==null){s=t;do i=s.lane,Y.lanes|=i,kn|=i,s=s.next;while(s!==t)}else s===null&&(n.lanes=0);return[e.memoizedState,n.dispatch]}function so(t){var e=Ge(),n=e.queue;if(n===null)throw Error(E(311));n.lastRenderedReducer=t;var r=n.dispatch,s=n.pending,i=e.memoizedState;if(s!==null){n.pending=null;var a=s=s.next;do i=t(i,a.action),a=a.next;while(a!==s);ot(i,e.memoizedState)||(Oe=!0),e.memoizedState=i,e.baseQueue===null&&(e.baseState=i),n.lastRenderedState=i}return[i,r]}function Eh(){}function Ph(t,e){var n=Y,r=Ge(),s=e(),i=!ot(r.memoizedState,s);if(i&&(r.memoizedState=s,Oe=!0),r=r.queue,cu(Lh.bind(null,n,r,t),[t]),r.getSnapshot!==e||i||ce!==null&&ce.memoizedState.tag&1){if(n.flags|=2048,ps(9,Nh.bind(null,n,r,s,e),void 0,null),fe===null)throw Error(E(349));Sn&30||Rh(n,e,s)}return s}function Rh(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=Y.updateQueue,e===null?(e={lastEffect:null,stores:null},Y.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Nh(t,e,n,r){e.value=n,e.getSnapshot=r,Oh(e)&&Ah(t)}function Lh(t,e,n){return n(function(){Oh(e)&&Ah(t)})}function Oh(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!ot(t,n)}catch{return!0}}function Ah(t){var e=Ot(t,1);e!==null&&at(e,t,1,-1)}function Jc(t){var e=ht();return typeof t=="function"&&(t=t()),e.memoizedState=e.baseState=t,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:hs,lastRenderedState:t},e.queue=t,t=t.dispatch=Qy.bind(null,Y,t),[e.memoizedState,t]}function ps(t,e,n,r){return t={tag:t,create:e,destroy:n,deps:r,next:null},e=Y.updateQueue,e===null?(e={lastEffect:null,stores:null},Y.updateQueue=e,e.lastEffect=t.next=t):(n=e.lastEffect,n===null?e.lastEffect=t.next=t:(r=n.next,n.next=t,t.next=r,e.lastEffect=t)),t}function Ih(){return Ge().memoizedState}function gi(t,e,n,r){var s=ht();Y.flags|=t,s.memoizedState=ps(1|e,n,void 0,r===void 0?null:r)}function da(t,e,n,r){var s=Ge();r=r===void 0?null:r;var i=void 0;if(ie!==null){var a=ie.memoizedState;if(i=a.destroy,r!==null&&ou(r,a.deps)){s.memoizedState=ps(e,n,i,r);return}}Y.flags|=t,s.memoizedState=ps(1|e,n,i,r)}function Xc(t,e){return gi(8390656,8,t,e)}function cu(t,e){return da(2048,8,t,e)}function bh(t,e){return da(4,2,t,e)}function Th(t,e){return da(4,4,t,e)}function $h(t,e){if(typeof e=="function")return t=t(),e(t),function(){e(null)};if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Fh(t,e,n){return n=n!=null?n.concat([t]):null,da(4,4,$h.bind(null,e,t),n)}function fu(){}function Mh(t,e){var n=Ge();e=e===void 0?null:e;var r=n.memoizedState;return r!==null&&e!==null&&ou(e,r[1])?r[0]:(n.memoizedState=[t,e],t)}function jh(t,e){var n=Ge();e=e===void 0?null:e;var r=n.memoizedState;return r!==null&&e!==null&&ou(e,r[1])?r[0]:(t=t(),n.memoizedState=[t,e],t)}function Dh(t,e,n){return Sn&21?(ot(n,e)||(n=Hd(),Y.lanes|=n,kn|=n,t.baseState=!0),e):(t.baseState&&(t.baseState=!1,Oe=!0),t.memoizedState=n)}function Wy(t,e){var n=U;U=n!==0&&4>n?n:4,t(!0);var r=no.transition;no.transition={};try{t(!1),e()}finally{U=n,no.transition=r}}function zh(){return Ge().memoizedState}function Ky(t,e,n){var r=Gt(t);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Uh(t))Bh(e,n);else if(n=Sh(t,e,n,r),n!==null){var s=Ee();at(n,t,r,s),Vh(n,e,r)}}function Qy(t,e,n){var r=Gt(t),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Uh(t))Bh(e,s);else{var i=t.alternate;if(t.lanes===0&&(i===null||i.lanes===0)&&(i=e.lastRenderedReducer,i!==null))try{var a=e.lastRenderedState,o=i(a,n);if(s.hasEagerState=!0,s.eagerState=o,ot(o,a)){var l=e.interleaved;l===null?(s.next=s,nu(e)):(s.next=l.next,l.next=s),e.interleaved=s;return}}catch{}finally{}n=Sh(t,e,s,r),n!==null&&(s=Ee(),at(n,t,r,s),Vh(n,e,r))}}function Uh(t){var e=t.alternate;return t===Y||e!==null&&e===Y}function Bh(t,e){Kr=Hi=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function Vh(t,e,n){if(n&4194240){var r=e.lanes;r&=t.pendingLanes,n|=r,e.lanes=n,Bl(t,n)}}var Wi={readContext:Xe,useCallback:ve,useContext:ve,useEffect:ve,useImperativeHandle:ve,useInsertionEffect:ve,useLayoutEffect:ve,useMemo:ve,useReducer:ve,useRef:ve,useState:ve,useDebugValue:ve,useDeferredValue:ve,useTransition:ve,useMutableSource:ve,useSyncExternalStore:ve,useId:ve,unstable_isNewReconciler:!1},Jy={readContext:Xe,useCallback:function(t,e){return ht().memoizedState=[t,e===void 0?null:e],t},useContext:Xe,useEffect:Xc,useImperativeHandle:function(t,e,n){return n=n!=null?n.concat([t]):null,gi(4194308,4,$h.bind(null,e,t),n)},useLayoutEffect:function(t,e){return gi(4194308,4,t,e)},useInsertionEffect:function(t,e){return gi(4,2,t,e)},useMemo:function(t,e){var n=ht();return e=e===void 0?null:e,t=t(),n.memoizedState=[t,e],t},useReducer:function(t,e,n){var r=ht();return e=n!==void 0?n(e):e,r.memoizedState=r.baseState=e,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:e},r.queue=t,t=t.dispatch=Ky.bind(null,Y,t),[r.memoizedState,t]},useRef:function(t){var e=ht();return t={current:t},e.memoizedState=t},useState:Jc,useDebugValue:fu,useDeferredValue:function(t){return ht().memoizedState=t},useTransition:function(){var t=Jc(!1),e=t[0];return t=Wy.bind(null,t[1]),ht().memoizedState=t,[e,t]},useMutableSource:function(){},useSyncExternalStore:function(t,e,n){var r=Y,s=ht();if(J){if(n===void 0)throw Error(E(407));n=n()}else{if(n=e(),fe===null)throw Error(E(349));Sn&30||Rh(r,e,n)}s.memoizedState=n;var i={value:n,getSnapshot:e};return s.queue=i,Xc(Lh.bind(null,r,i,t),[t]),r.flags|=2048,ps(9,Nh.bind(null,r,i,n,e),void 0,null),n},useId:function(){var t=ht(),e=fe.identifierPrefix;if(J){var n=Pt,r=Et;n=(r&~(1<<32-it(r)-1)).toString(32)+n,e=":"+e+"R"+n,n=ds++,0<n&&(e+="H"+n.toString(32)),e+=":"}else n=Hy++,e=":"+e+"r"+n.toString(32)+":";return t.memoizedState=e},unstable_isNewReconciler:!1},Xy={readContext:Xe,useCallback:Mh,useContext:Xe,useEffect:cu,useImperativeHandle:Fh,useInsertionEffect:bh,useLayoutEffect:Th,useMemo:jh,useReducer:ro,useRef:Ih,useState:function(){return ro(hs)},useDebugValue:fu,useDeferredValue:function(t){var e=Ge();return Dh(e,ie.memoizedState,t)},useTransition:function(){var t=ro(hs)[0],e=Ge().memoizedState;return[t,e]},useMutableSource:Eh,useSyncExternalStore:Ph,useId:zh,unstable_isNewReconciler:!1},Gy={readContext:Xe,useCallback:Mh,useContext:Xe,useEffect:cu,useImperativeHandle:Fh,useInsertionEffect:bh,useLayoutEffect:Th,useMemo:jh,useReducer:so,useRef:Ih,useState:function(){return so(hs)},useDebugValue:fu,useDeferredValue:function(t){var e=Ge();return ie===null?e.memoizedState=t:Dh(e,ie.memoizedState,t)},useTransition:function(){var t=so(hs)[0],e=Ge().memoizedState;return[t,e]},useMutableSource:Eh,useSyncExternalStore:Ph,useId:zh,unstable_isNewReconciler:!1};function et(t,e){if(t&&t.defaultProps){e=q({},e),t=t.defaultProps;for(var n in t)e[n]===void 0&&(e[n]=t[n]);return e}return e}function qo(t,e,n,r){e=t.memoizedState,n=n(r,e),n=n==null?e:q({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var ha={isMounted:function(t){return(t=t._reactInternals)?Rn(t)===t:!1},enqueueSetState:function(t,e,n){t=t._reactInternals;var r=Ee(),s=Gt(t),i=Rt(r,s);i.payload=e,n!=null&&(i.callback=n),e=Jt(t,i,s),e!==null&&(at(e,t,s,r),pi(e,t,s))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var r=Ee(),s=Gt(t),i=Rt(r,s);i.tag=1,i.payload=e,n!=null&&(i.callback=n),e=Jt(t,i,s),e!==null&&(at(e,t,s,r),pi(e,t,s))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=Ee(),r=Gt(t),s=Rt(n,r);s.tag=2,e!=null&&(s.callback=e),e=Jt(t,s,r),e!==null&&(at(e,t,r,n),pi(e,t,r))}};function Gc(t,e,n,r,s,i,a){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(r,i,a):e.prototype&&e.prototype.isPureReactComponent?!as(n,r)||!as(s,i):!0}function Hh(t,e,n){var r=!1,s=en,i=e.contextType;return typeof i=="object"&&i!==null?i=Xe(i):(s=Ie(e)?xn:Se.current,r=e.contextTypes,i=(r=r!=null)?or(t,s):en),e=new e(n,i),t.memoizedState=e.state!==null&&e.state!==void 0?e.state:null,e.updater=ha,t.stateNode=e,e._reactInternals=t,r&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=s,t.__reactInternalMemoizedMaskedChildContext=i),e}function Yc(t,e,n,r){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,r),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,r),e.state!==t&&ha.enqueueReplaceState(e,e.state,null)}function Zo(t,e,n,r){var s=t.stateNode;s.props=n,s.state=t.memoizedState,s.refs={},ru(t);var i=e.contextType;typeof i=="object"&&i!==null?s.context=Xe(i):(i=Ie(e)?xn:Se.current,s.context=or(t,i)),s.state=t.memoizedState,i=e.getDerivedStateFromProps,typeof i=="function"&&(qo(t,e,i,n),s.state=t.memoizedState),typeof e.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(e=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),e!==s.state&&ha.enqueueReplaceState(s,s.state,null),Bi(t,n,s,r),s.state=t.memoizedState),typeof s.componentDidMount=="function"&&(t.flags|=4194308)}function fr(t,e){try{var n="",r=e;do n+=Cg(r),r=r.return;while(r);var s=n}catch(i){s=`
Error generating stack: `+i.message+`
`+i.stack}return{value:t,source:e,stack:s,digest:null}}function io(t,e,n){return{value:t,source:null,stack:n??null,digest:e??null}}function el(t,e){try{console.error(e.value)}catch(n){setTimeout(function(){throw n})}}var Yy=typeof WeakMap=="function"?WeakMap:Map;function Wh(t,e,n){n=Rt(-1,n),n.tag=3,n.payload={element:null};var r=e.value;return n.callback=function(){Qi||(Qi=!0,cl=r),el(t,e)},n}function Kh(t,e,n){n=Rt(-1,n),n.tag=3;var r=t.type.getDerivedStateFromError;if(typeof r=="function"){var s=e.value;n.payload=function(){return r(s)},n.callback=function(){el(t,e)}}var i=t.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){el(t,e),typeof r!="function"&&(Xt===null?Xt=new Set([this]):Xt.add(this));var a=e.stack;this.componentDidCatch(e.value,{componentStack:a!==null?a:""})}),n}function qc(t,e,n){var r=t.pingCache;if(r===null){r=t.pingCache=new Yy;var s=new Set;r.set(e,s)}else s=r.get(e),s===void 0&&(s=new Set,r.set(e,s));s.has(n)||(s.add(n),t=f0.bind(null,t,e,n),e.then(t,t))}function Zc(t){do{var e;if((e=t.tag===13)&&(e=t.memoizedState,e=e!==null?e.dehydrated!==null:!0),e)return t;t=t.return}while(t!==null);return null}function ef(t,e,n,r,s){return t.mode&1?(t.flags|=65536,t.lanes=s,t):(t===e?t.flags|=65536:(t.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(e=Rt(-1,1),e.tag=2,Jt(n,e,1))),n.lanes|=1),t)}var qy=It.ReactCurrentOwner,Oe=!1;function Ce(t,e,n,r){e.child=t===null?_h(e,null,n,r):ur(e,t.child,n,r)}function tf(t,e,n,r,s){n=n.render;var i=e.ref;return tr(e,s),r=lu(t,e,n,r,i,s),n=uu(),t!==null&&!Oe?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~s,At(t,e,s)):(J&&n&&Gl(e),e.flags|=1,Ce(t,e,r,s),e.child)}function nf(t,e,n,r,s){if(t===null){var i=n.type;return typeof i=="function"&&!wu(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(e.tag=15,e.type=i,Qh(t,e,i,r,s)):(t=xi(n.type,null,r,e,e.mode,s),t.ref=e.ref,t.return=e,e.child=t)}if(i=t.child,!(t.lanes&s)){var a=i.memoizedProps;if(n=n.compare,n=n!==null?n:as,n(a,r)&&t.ref===e.ref)return At(t,e,s)}return e.flags|=1,t=Yt(i,r),t.ref=e.ref,t.return=e,e.child=t}function Qh(t,e,n,r,s){if(t!==null){var i=t.memoizedProps;if(as(i,r)&&t.ref===e.ref)if(Oe=!1,e.pendingProps=r=i,(t.lanes&s)!==0)t.flags&131072&&(Oe=!0);else return e.lanes=t.lanes,At(t,e,s)}return tl(t,e,n,r,s)}function Jh(t,e,n){var r=e.pendingProps,s=r.children,i=t!==null?t.memoizedState:null;if(r.mode==="hidden")if(!(e.mode&1))e.memoizedState={baseLanes:0,cachePool:null,transitions:null},H(Xn,je),je|=n;else{if(!(n&1073741824))return t=i!==null?i.baseLanes|n:n,e.lanes=e.childLanes=1073741824,e.memoizedState={baseLanes:t,cachePool:null,transitions:null},e.updateQueue=null,H(Xn,je),je|=t,null;e.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,H(Xn,je),je|=r}else i!==null?(r=i.baseLanes|n,e.memoizedState=null):r=n,H(Xn,je),je|=r;return Ce(t,e,s,n),e.child}function Xh(t,e){var n=e.ref;(t===null&&n!==null||t!==null&&t.ref!==n)&&(e.flags|=512,e.flags|=2097152)}function tl(t,e,n,r,s){var i=Ie(n)?xn:Se.current;return i=or(e,i),tr(e,s),n=lu(t,e,n,r,i,s),r=uu(),t!==null&&!Oe?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~s,At(t,e,s)):(J&&r&&Gl(e),e.flags|=1,Ce(t,e,n,s),e.child)}function rf(t,e,n,r,s){if(Ie(n)){var i=!0;Mi(e)}else i=!1;if(tr(e,s),e.stateNode===null)yi(t,e),Hh(e,n,r),Zo(e,n,r,s),r=!0;else if(t===null){var a=e.stateNode,o=e.memoizedProps;a.props=o;var l=a.context,u=n.contextType;typeof u=="object"&&u!==null?u=Xe(u):(u=Ie(n)?xn:Se.current,u=or(e,u));var h=n.getDerivedStateFromProps,d=typeof h=="function"||typeof a.getSnapshotBeforeUpdate=="function";d||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(o!==r||l!==u)&&Yc(e,a,r,u),Dt=!1;var c=e.memoizedState;a.state=c,Bi(e,r,a,s),l=e.memoizedState,o!==r||c!==l||Ae.current||Dt?(typeof h=="function"&&(qo(e,n,h,r),l=e.memoizedState),(o=Dt||Gc(e,n,o,r,c,l,u))?(d||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(e.flags|=4194308)):(typeof a.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=r,e.memoizedState=l),a.props=r,a.state=l,a.context=u,r=o):(typeof a.componentDidMount=="function"&&(e.flags|=4194308),r=!1)}else{a=e.stateNode,kh(t,e),o=e.memoizedProps,u=e.type===e.elementType?o:et(e.type,o),a.props=u,d=e.pendingProps,c=a.context,l=n.contextType,typeof l=="object"&&l!==null?l=Xe(l):(l=Ie(n)?xn:Se.current,l=or(e,l));var g=n.getDerivedStateFromProps;(h=typeof g=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(o!==d||c!==l)&&Yc(e,a,r,l),Dt=!1,c=e.memoizedState,a.state=c,Bi(e,r,a,s);var v=e.memoizedState;o!==d||c!==v||Ae.current||Dt?(typeof g=="function"&&(qo(e,n,g,r),v=e.memoizedState),(u=Dt||Gc(e,n,u,r,c,v,l)||!1)?(h||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,v,l),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,v,l)),typeof a.componentDidUpdate=="function"&&(e.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof a.componentDidUpdate!="function"||o===t.memoizedProps&&c===t.memoizedState||(e.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&c===t.memoizedState||(e.flags|=1024),e.memoizedProps=r,e.memoizedState=v),a.props=r,a.state=v,a.context=l,r=u):(typeof a.componentDidUpdate!="function"||o===t.memoizedProps&&c===t.memoizedState||(e.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&c===t.memoizedState||(e.flags|=1024),r=!1)}return nl(t,e,n,r,i,s)}function nl(t,e,n,r,s,i){Xh(t,e);var a=(e.flags&128)!==0;if(!r&&!a)return s&&Bc(e,n,!1),At(t,e,i);r=e.stateNode,qy.current=e;var o=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return e.flags|=1,t!==null&&a?(e.child=ur(e,t.child,null,i),e.child=ur(e,null,o,i)):Ce(t,e,o,i),e.memoizedState=r.state,s&&Bc(e,n,!0),e.child}function Gh(t){var e=t.stateNode;e.pendingContext?Uc(t,e.pendingContext,e.pendingContext!==e.context):e.context&&Uc(t,e.context,!1),su(t,e.containerInfo)}function sf(t,e,n,r,s){return lr(),ql(s),e.flags|=256,Ce(t,e,n,r),e.child}var rl={dehydrated:null,treeContext:null,retryLane:0};function sl(t){return{baseLanes:t,cachePool:null,transitions:null}}function Yh(t,e,n){var r=e.pendingProps,s=G.current,i=!1,a=(e.flags&128)!==0,o;if((o=a)||(o=t!==null&&t.memoizedState===null?!1:(s&2)!==0),o?(i=!0,e.flags&=-129):(t===null||t.memoizedState!==null)&&(s|=1),H(G,s&1),t===null)return Go(e),t=e.memoizedState,t!==null&&(t=t.dehydrated,t!==null)?(e.mode&1?t.data==="$!"?e.lanes=8:e.lanes=1073741824:e.lanes=1,null):(a=r.children,t=r.fallback,i?(r=e.mode,i=e.child,a={mode:"hidden",children:a},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=a):i=ga(a,r,0,null),t=vn(t,r,n,null),i.return=e,t.return=e,i.sibling=t,e.child=i,e.child.memoizedState=sl(n),e.memoizedState=rl,t):du(e,a));if(s=t.memoizedState,s!==null&&(o=s.dehydrated,o!==null))return Zy(t,e,a,r,o,s,n);if(i){i=r.fallback,a=e.mode,s=t.child,o=s.sibling;var l={mode:"hidden",children:r.children};return!(a&1)&&e.child!==s?(r=e.child,r.childLanes=0,r.pendingProps=l,e.deletions=null):(r=Yt(s,l),r.subtreeFlags=s.subtreeFlags&14680064),o!==null?i=Yt(o,i):(i=vn(i,a,n,null),i.flags|=2),i.return=e,r.return=e,r.sibling=i,e.child=r,r=i,i=e.child,a=t.child.memoizedState,a=a===null?sl(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},i.memoizedState=a,i.childLanes=t.childLanes&~n,e.memoizedState=rl,r}return i=t.child,t=i.sibling,r=Yt(i,{mode:"visible",children:r.children}),!(e.mode&1)&&(r.lanes=n),r.return=e,r.sibling=null,t!==null&&(n=e.deletions,n===null?(e.deletions=[t],e.flags|=16):n.push(t)),e.child=r,e.memoizedState=null,r}function du(t,e){return e=ga({mode:"visible",children:e},t.mode,0,null),e.return=t,t.child=e}function Qs(t,e,n,r){return r!==null&&ql(r),ur(e,t.child,null,n),t=du(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Zy(t,e,n,r,s,i,a){if(n)return e.flags&256?(e.flags&=-257,r=io(Error(E(422))),Qs(t,e,a,r)):e.memoizedState!==null?(e.child=t.child,e.flags|=128,null):(i=r.fallback,s=e.mode,r=ga({mode:"visible",children:r.children},s,0,null),i=vn(i,s,a,null),i.flags|=2,r.return=e,i.return=e,r.sibling=i,e.child=r,e.mode&1&&ur(e,t.child,null,a),e.child.memoizedState=sl(a),e.memoizedState=rl,i);if(!(e.mode&1))return Qs(t,e,a,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var o=r.dgst;return r=o,i=Error(E(419)),r=io(i,r,void 0),Qs(t,e,a,r)}if(o=(a&t.childLanes)!==0,Oe||o){if(r=fe,r!==null){switch(a&-a){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|a)?0:s,s!==0&&s!==i.retryLane&&(i.retryLane=s,Ot(t,s),at(r,t,s,-1))}return vu(),r=io(Error(E(421))),Qs(t,e,a,r)}return s.data==="$?"?(e.flags|=128,e.child=t.child,e=d0.bind(null,t),s._reactRetry=e,null):(t=i.treeContext,De=Qt(s.nextSibling),ze=e,J=!0,rt=null,t!==null&&(He[We++]=Et,He[We++]=Pt,He[We++]=_n,Et=t.id,Pt=t.overflow,_n=e),e=du(e,r.children),e.flags|=4096,e)}function af(t,e,n){t.lanes|=e;var r=t.alternate;r!==null&&(r.lanes|=e),Yo(t.return,e,n)}function ao(t,e,n,r,s){var i=t.memoizedState;i===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(i.isBackwards=e,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=s)}function qh(t,e,n){var r=e.pendingProps,s=r.revealOrder,i=r.tail;if(Ce(t,e,r.children,n),r=G.current,r&2)r=r&1|2,e.flags|=128;else{if(t!==null&&t.flags&128)e:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&af(t,n,e);else if(t.tag===19)af(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;t=t.return}t.sibling.return=t.return,t=t.sibling}r&=1}if(H(G,r),!(e.mode&1))e.memoizedState=null;else switch(s){case"forwards":for(n=e.child,s=null;n!==null;)t=n.alternate,t!==null&&Vi(t)===null&&(s=n),n=n.sibling;n=s,n===null?(s=e.child,e.child=null):(s=n.sibling,n.sibling=null),ao(e,!1,s,n,i);break;case"backwards":for(n=null,s=e.child,e.child=null;s!==null;){if(t=s.alternate,t!==null&&Vi(t)===null){e.child=s;break}t=s.sibling,s.sibling=n,n=s,s=t}ao(e,!0,n,null,i);break;case"together":ao(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function yi(t,e){!(e.mode&1)&&t!==null&&(t.alternate=null,e.alternate=null,e.flags|=2)}function At(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),kn|=e.lanes,!(n&e.childLanes))return null;if(t!==null&&e.child!==t.child)throw Error(E(153));if(e.child!==null){for(t=e.child,n=Yt(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=Yt(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function e0(t,e,n){switch(e.tag){case 3:Gh(e),lr();break;case 5:Ch(e);break;case 1:Ie(e.type)&&Mi(e);break;case 4:su(e,e.stateNode.containerInfo);break;case 10:var r=e.type._context,s=e.memoizedProps.value;H(zi,r._currentValue),r._currentValue=s;break;case 13:if(r=e.memoizedState,r!==null)return r.dehydrated!==null?(H(G,G.current&1),e.flags|=128,null):n&e.child.childLanes?Yh(t,e,n):(H(G,G.current&1),t=At(t,e,n),t!==null?t.sibling:null);H(G,G.current&1);break;case 19:if(r=(n&e.childLanes)!==0,t.flags&128){if(r)return qh(t,e,n);e.flags|=128}if(s=e.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),H(G,G.current),r)break;return null;case 22:case 23:return e.lanes=0,Jh(t,e,n)}return At(t,e,n)}var Zh,il,ep,tp;Zh=function(t,e){for(var n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};il=function(){};ep=function(t,e,n,r){var s=t.memoizedProps;if(s!==r){t=e.stateNode,mn(xt.current);var i=null;switch(n){case"input":s=Ro(t,s),r=Ro(t,r),i=[];break;case"select":s=q({},s,{value:void 0}),r=q({},r,{value:void 0}),i=[];break;case"textarea":s=Oo(t,s),r=Oo(t,r),i=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(t.onclick=$i)}Io(n,r);var a;n=null;for(u in s)if(!r.hasOwnProperty(u)&&s.hasOwnProperty(u)&&s[u]!=null)if(u==="style"){var o=s[u];for(a in o)o.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Zr.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var l=r[u];if(o=s!=null?s[u]:void 0,r.hasOwnProperty(u)&&l!==o&&(l!=null||o!=null))if(u==="style")if(o){for(a in o)!o.hasOwnProperty(a)||l&&l.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in l)l.hasOwnProperty(a)&&o[a]!==l[a]&&(n||(n={}),n[a]=l[a])}else n||(i||(i=[]),i.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,o=o?o.__html:void 0,l!=null&&o!==l&&(i=i||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(i=i||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Zr.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&W("scroll",t),i||o===l||(i=[])):(i=i||[]).push(u,l))}n&&(i=i||[]).push("style",n);var u=i;(e.updateQueue=u)&&(e.flags|=4)}};tp=function(t,e,n,r){n!==r&&(e.flags|=4)};function Pr(t,e){if(!J)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:r.sibling=null}}function we(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,r=0;if(e)for(var s=t.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=t,s=s.sibling;else for(s=t.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=t,s=s.sibling;return t.subtreeFlags|=r,t.childLanes=n,e}function t0(t,e,n){var r=e.pendingProps;switch(Yl(e),e.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return we(e),null;case 1:return Ie(e.type)&&Fi(),we(e),null;case 3:return r=e.stateNode,cr(),K(Ae),K(Se),au(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(t===null||t.child===null)&&(Ws(e)?e.flags|=4:t===null||t.memoizedState.isDehydrated&&!(e.flags&256)||(e.flags|=1024,rt!==null&&(hl(rt),rt=null))),il(t,e),we(e),null;case 5:iu(e);var s=mn(fs.current);if(n=e.type,t!==null&&e.stateNode!=null)ep(t,e,n,r,s),t.ref!==e.ref&&(e.flags|=512,e.flags|=2097152);else{if(!r){if(e.stateNode===null)throw Error(E(166));return we(e),null}if(t=mn(xt.current),Ws(e)){r=e.stateNode,n=e.type;var i=e.memoizedProps;switch(r[gt]=e,r[us]=i,t=(e.mode&1)!==0,n){case"dialog":W("cancel",r),W("close",r);break;case"iframe":case"object":case"embed":W("load",r);break;case"video":case"audio":for(s=0;s<Tr.length;s++)W(Tr[s],r);break;case"source":W("error",r);break;case"img":case"image":case"link":W("error",r),W("load",r);break;case"details":W("toggle",r);break;case"input":pc(r,i),W("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},W("invalid",r);break;case"textarea":gc(r,i),W("invalid",r)}Io(n,i),s=null;for(var a in i)if(i.hasOwnProperty(a)){var o=i[a];a==="children"?typeof o=="string"?r.textContent!==o&&(i.suppressHydrationWarning!==!0&&Hs(r.textContent,o,t),s=["children",o]):typeof o=="number"&&r.textContent!==""+o&&(i.suppressHydrationWarning!==!0&&Hs(r.textContent,o,t),s=["children",""+o]):Zr.hasOwnProperty(a)&&o!=null&&a==="onScroll"&&W("scroll",r)}switch(n){case"input":Fs(r),mc(r,i,!0);break;case"textarea":Fs(r),yc(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=$i)}r=s,e.updateQueue=r,r!==null&&(e.flags|=4)}else{a=s.nodeType===9?s:s.ownerDocument,t==="http://www.w3.org/1999/xhtml"&&(t=Ld(n)),t==="http://www.w3.org/1999/xhtml"?n==="script"?(t=a.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild)):typeof r.is=="string"?t=a.createElement(n,{is:r.is}):(t=a.createElement(n),n==="select"&&(a=t,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):t=a.createElementNS(t,n),t[gt]=e,t[us]=r,Zh(t,e,!1,!1),e.stateNode=t;e:{switch(a=bo(n,r),n){case"dialog":W("cancel",t),W("close",t),s=r;break;case"iframe":case"object":case"embed":W("load",t),s=r;break;case"video":case"audio":for(s=0;s<Tr.length;s++)W(Tr[s],t);s=r;break;case"source":W("error",t),s=r;break;case"img":case"image":case"link":W("error",t),W("load",t),s=r;break;case"details":W("toggle",t),s=r;break;case"input":pc(t,r),s=Ro(t,r),W("invalid",t);break;case"option":s=r;break;case"select":t._wrapperState={wasMultiple:!!r.multiple},s=q({},r,{value:void 0}),W("invalid",t);break;case"textarea":gc(t,r),s=Oo(t,r),W("invalid",t);break;default:s=r}Io(n,s),o=s;for(i in o)if(o.hasOwnProperty(i)){var l=o[i];i==="style"?Id(t,l):i==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Od(t,l)):i==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&es(t,l):typeof l=="number"&&es(t,""+l):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Zr.hasOwnProperty(i)?l!=null&&i==="onScroll"&&W("scroll",t):l!=null&&Fl(t,i,l,a))}switch(n){case"input":Fs(t),mc(t,r,!1);break;case"textarea":Fs(t),yc(t);break;case"option":r.value!=null&&t.setAttribute("value",""+Zt(r.value));break;case"select":t.multiple=!!r.multiple,i=r.value,i!=null?Yn(t,!!r.multiple,i,!1):r.defaultValue!=null&&Yn(t,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(t.onclick=$i)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(e.flags|=4)}e.ref!==null&&(e.flags|=512,e.flags|=2097152)}return we(e),null;case 6:if(t&&e.stateNode!=null)tp(t,e,t.memoizedProps,r);else{if(typeof r!="string"&&e.stateNode===null)throw Error(E(166));if(n=mn(fs.current),mn(xt.current),Ws(e)){if(r=e.stateNode,n=e.memoizedProps,r[gt]=e,(i=r.nodeValue!==n)&&(t=ze,t!==null))switch(t.tag){case 3:Hs(r.nodeValue,n,(t.mode&1)!==0);break;case 5:t.memoizedProps.suppressHydrationWarning!==!0&&Hs(r.nodeValue,n,(t.mode&1)!==0)}i&&(e.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[gt]=e,e.stateNode=r}return we(e),null;case 13:if(K(G),r=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(J&&De!==null&&e.mode&1&&!(e.flags&128))wh(),lr(),e.flags|=98560,i=!1;else if(i=Ws(e),r!==null&&r.dehydrated!==null){if(t===null){if(!i)throw Error(E(318));if(i=e.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(E(317));i[gt]=e}else lr(),!(e.flags&128)&&(e.memoizedState=null),e.flags|=4;we(e),i=!1}else rt!==null&&(hl(rt),rt=null),i=!0;if(!i)return e.flags&65536?e:null}return e.flags&128?(e.lanes=n,e):(r=r!==null,r!==(t!==null&&t.memoizedState!==null)&&r&&(e.child.flags|=8192,e.mode&1&&(t===null||G.current&1?ae===0&&(ae=3):vu())),e.updateQueue!==null&&(e.flags|=4),we(e),null);case 4:return cr(),il(t,e),t===null&&os(e.stateNode.containerInfo),we(e),null;case 10:return tu(e.type._context),we(e),null;case 17:return Ie(e.type)&&Fi(),we(e),null;case 19:if(K(G),i=e.memoizedState,i===null)return we(e),null;if(r=(e.flags&128)!==0,a=i.rendering,a===null)if(r)Pr(i,!1);else{if(ae!==0||t!==null&&t.flags&128)for(t=e.child;t!==null;){if(a=Vi(t),a!==null){for(e.flags|=128,Pr(i,!1),r=a.updateQueue,r!==null&&(e.updateQueue=r,e.flags|=4),e.subtreeFlags=0,r=n,n=e.child;n!==null;)i=n,t=r,i.flags&=14680066,a=i.alternate,a===null?(i.childLanes=0,i.lanes=t,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=a.childLanes,i.lanes=a.lanes,i.child=a.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=a.memoizedProps,i.memoizedState=a.memoizedState,i.updateQueue=a.updateQueue,i.type=a.type,t=a.dependencies,i.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),n=n.sibling;return H(G,G.current&1|2),e.child}t=t.sibling}i.tail!==null&&te()>dr&&(e.flags|=128,r=!0,Pr(i,!1),e.lanes=4194304)}else{if(!r)if(t=Vi(a),t!==null){if(e.flags|=128,r=!0,n=t.updateQueue,n!==null&&(e.updateQueue=n,e.flags|=4),Pr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!a.alternate&&!J)return we(e),null}else 2*te()-i.renderingStartTime>dr&&n!==1073741824&&(e.flags|=128,r=!0,Pr(i,!1),e.lanes=4194304);i.isBackwards?(a.sibling=e.child,e.child=a):(n=i.last,n!==null?n.sibling=a:e.child=a,i.last=a)}return i.tail!==null?(e=i.tail,i.rendering=e,i.tail=e.sibling,i.renderingStartTime=te(),e.sibling=null,n=G.current,H(G,r?n&1|2:n&1),e):(we(e),null);case 22:case 23:return yu(),r=e.memoizedState!==null,t!==null&&t.memoizedState!==null!==r&&(e.flags|=8192),r&&e.mode&1?je&1073741824&&(we(e),e.subtreeFlags&6&&(e.flags|=8192)):we(e),null;case 24:return null;case 25:return null}throw Error(E(156,e.tag))}function n0(t,e){switch(Yl(e),e.tag){case 1:return Ie(e.type)&&Fi(),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return cr(),K(Ae),K(Se),au(),t=e.flags,t&65536&&!(t&128)?(e.flags=t&-65537|128,e):null;case 5:return iu(e),null;case 13:if(K(G),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(E(340));lr()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return K(G),null;case 4:return cr(),null;case 10:return tu(e.type._context),null;case 22:case 23:return yu(),null;case 24:return null;default:return null}}var Js=!1,_e=!1,r0=typeof WeakSet=="function"?WeakSet:Set,N=null;function Jn(t,e){var n=t.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ee(t,e,r)}else n.current=null}function al(t,e,n){try{n()}catch(r){ee(t,e,r)}}var of=!1;function s0(t,e){if(Vo=Ii,t=ah(),Xl(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else e:{n=(n=t.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var a=0,o=-1,l=-1,u=0,h=0,d=t,c=null;t:for(;;){for(var g;d!==n||s!==0&&d.nodeType!==3||(o=a+s),d!==i||r!==0&&d.nodeType!==3||(l=a+r),d.nodeType===3&&(a+=d.nodeValue.length),(g=d.firstChild)!==null;)c=d,d=g;for(;;){if(d===t)break t;if(c===n&&++u===s&&(o=a),c===i&&++h===r&&(l=a),(g=d.nextSibling)!==null)break;d=c,c=d.parentNode}d=g}n=o===-1||l===-1?null:{start:o,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ho={focusedElem:t,selectionRange:n},Ii=!1,N=e;N!==null;)if(e=N,t=e.child,(e.subtreeFlags&1028)!==0&&t!==null)t.return=e,N=t;else for(;N!==null;){e=N;try{var v=e.alternate;if(e.flags&1024)switch(e.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var w=v.memoizedProps,_=v.memoizedState,m=e.stateNode,f=m.getSnapshotBeforeUpdate(e.elementType===e.type?w:et(e.type,w),_);m.__reactInternalSnapshotBeforeUpdate=f}break;case 3:var p=e.stateNode.containerInfo;p.nodeType===1?p.textContent="":p.nodeType===9&&p.documentElement&&p.removeChild(p.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(E(163))}}catch(y){ee(e,e.return,y)}if(t=e.sibling,t!==null){t.return=e.return,N=t;break}N=e.return}return v=of,of=!1,v}function Qr(t,e,n){var r=e.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&t)===t){var i=s.destroy;s.destroy=void 0,i!==void 0&&al(e,n,i)}s=s.next}while(s!==r)}}function pa(t,e){if(e=e.updateQueue,e=e!==null?e.lastEffect:null,e!==null){var n=e=e.next;do{if((n.tag&t)===t){var r=n.create;n.destroy=r()}n=n.next}while(n!==e)}}function ol(t){var e=t.ref;if(e!==null){var n=t.stateNode;switch(t.tag){case 5:t=n;break;default:t=n}typeof e=="function"?e(t):e.current=t}}function np(t){var e=t.alternate;e!==null&&(t.alternate=null,np(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&(delete e[gt],delete e[us],delete e[Qo],delete e[zy],delete e[Uy])),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}function rp(t){return t.tag===5||t.tag===3||t.tag===4}function lf(t){e:for(;;){for(;t.sibling===null;){if(t.return===null||rp(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue e;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function ll(t,e,n){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?n.nodeType===8?n.parentNode.insertBefore(t,e):n.insertBefore(t,e):(n.nodeType===8?(e=n.parentNode,e.insertBefore(t,n)):(e=n,e.appendChild(t)),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=$i));else if(r!==4&&(t=t.child,t!==null))for(ll(t,e,n),t=t.sibling;t!==null;)ll(t,e,n),t=t.sibling}function ul(t,e,n){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(r!==4&&(t=t.child,t!==null))for(ul(t,e,n),t=t.sibling;t!==null;)ul(t,e,n),t=t.sibling}var he=null,nt=!1;function Tt(t,e,n){for(n=n.child;n!==null;)sp(t,e,n),n=n.sibling}function sp(t,e,n){if(wt&&typeof wt.onCommitFiberUnmount=="function")try{wt.onCommitFiberUnmount(aa,n)}catch{}switch(n.tag){case 5:_e||Jn(n,e);case 6:var r=he,s=nt;he=null,Tt(t,e,n),he=r,nt=s,he!==null&&(nt?(t=he,n=n.stateNode,t.nodeType===8?t.parentNode.removeChild(n):t.removeChild(n)):he.removeChild(n.stateNode));break;case 18:he!==null&&(nt?(t=he,n=n.stateNode,t.nodeType===8?Za(t.parentNode,n):t.nodeType===1&&Za(t,n),ss(t)):Za(he,n.stateNode));break;case 4:r=he,s=nt,he=n.stateNode.containerInfo,nt=!0,Tt(t,e,n),he=r,nt=s;break;case 0:case 11:case 14:case 15:if(!_e&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var i=s,a=i.destroy;i=i.tag,a!==void 0&&(i&2||i&4)&&al(n,e,a),s=s.next}while(s!==r)}Tt(t,e,n);break;case 1:if(!_e&&(Jn(n,e),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(o){ee(n,e,o)}Tt(t,e,n);break;case 21:Tt(t,e,n);break;case 22:n.mode&1?(_e=(r=_e)||n.memoizedState!==null,Tt(t,e,n),_e=r):Tt(t,e,n);break;default:Tt(t,e,n)}}function uf(t){var e=t.updateQueue;if(e!==null){t.updateQueue=null;var n=t.stateNode;n===null&&(n=t.stateNode=new r0),e.forEach(function(r){var s=h0.bind(null,t,r);n.has(r)||(n.add(r),r.then(s,s))})}}function qe(t,e){var n=e.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var i=t,a=e,o=a;e:for(;o!==null;){switch(o.tag){case 5:he=o.stateNode,nt=!1;break e;case 3:he=o.stateNode.containerInfo,nt=!0;break e;case 4:he=o.stateNode.containerInfo,nt=!0;break e}o=o.return}if(he===null)throw Error(E(160));sp(i,a,s),he=null,nt=!1;var l=s.alternate;l!==null&&(l.return=null),s.return=null}catch(u){ee(s,e,u)}}if(e.subtreeFlags&12854)for(e=e.child;e!==null;)ip(e,t),e=e.sibling}function ip(t,e){var n=t.alternate,r=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:if(qe(e,t),ft(t),r&4){try{Qr(3,t,t.return),pa(3,t)}catch(w){ee(t,t.return,w)}try{Qr(5,t,t.return)}catch(w){ee(t,t.return,w)}}break;case 1:qe(e,t),ft(t),r&512&&n!==null&&Jn(n,n.return);break;case 5:if(qe(e,t),ft(t),r&512&&n!==null&&Jn(n,n.return),t.flags&32){var s=t.stateNode;try{es(s,"")}catch(w){ee(t,t.return,w)}}if(r&4&&(s=t.stateNode,s!=null)){var i=t.memoizedProps,a=n!==null?n.memoizedProps:i,o=t.type,l=t.updateQueue;if(t.updateQueue=null,l!==null)try{o==="input"&&i.type==="radio"&&i.name!=null&&Rd(s,i),bo(o,a);var u=bo(o,i);for(a=0;a<l.length;a+=2){var h=l[a],d=l[a+1];h==="style"?Id(s,d):h==="dangerouslySetInnerHTML"?Od(s,d):h==="children"?es(s,d):Fl(s,h,d,u)}switch(o){case"input":No(s,i);break;case"textarea":Nd(s,i);break;case"select":var c=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!i.multiple;var g=i.value;g!=null?Yn(s,!!i.multiple,g,!1):c!==!!i.multiple&&(i.defaultValue!=null?Yn(s,!!i.multiple,i.defaultValue,!0):Yn(s,!!i.multiple,i.multiple?[]:"",!1))}s[us]=i}catch(w){ee(t,t.return,w)}}break;case 6:if(qe(e,t),ft(t),r&4){if(t.stateNode===null)throw Error(E(162));s=t.stateNode,i=t.memoizedProps;try{s.nodeValue=i}catch(w){ee(t,t.return,w)}}break;case 3:if(qe(e,t),ft(t),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ss(e.containerInfo)}catch(w){ee(t,t.return,w)}break;case 4:qe(e,t),ft(t);break;case 13:qe(e,t),ft(t),s=t.child,s.flags&8192&&(i=s.memoizedState!==null,s.stateNode.isHidden=i,!i||s.alternate!==null&&s.alternate.memoizedState!==null||(mu=te())),r&4&&uf(t);break;case 22:if(h=n!==null&&n.memoizedState!==null,t.mode&1?(_e=(u=_e)||h,qe(e,t),_e=u):qe(e,t),ft(t),r&8192){if(u=t.memoizedState!==null,(t.stateNode.isHidden=u)&&!h&&t.mode&1)for(N=t,h=t.child;h!==null;){for(d=N=h;N!==null;){switch(c=N,g=c.child,c.tag){case 0:case 11:case 14:case 15:Qr(4,c,c.return);break;case 1:Jn(c,c.return);var v=c.stateNode;if(typeof v.componentWillUnmount=="function"){r=c,n=c.return;try{e=r,v.props=e.memoizedProps,v.state=e.memoizedState,v.componentWillUnmount()}catch(w){ee(r,n,w)}}break;case 5:Jn(c,c.return);break;case 22:if(c.memoizedState!==null){ff(d);continue}}g!==null?(g.return=c,N=g):ff(d)}h=h.sibling}e:for(h=null,d=t;;){if(d.tag===5){if(h===null){h=d;try{s=d.stateNode,u?(i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(o=d.stateNode,l=d.memoizedProps.style,a=l!=null&&l.hasOwnProperty("display")?l.display:null,o.style.display=Ad("display",a))}catch(w){ee(t,t.return,w)}}}else if(d.tag===6){if(h===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(w){ee(t,t.return,w)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===t)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===t)break e;for(;d.sibling===null;){if(d.return===null||d.return===t)break e;h===d&&(h=null),d=d.return}h===d&&(h=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:qe(e,t),ft(t),r&4&&uf(t);break;case 21:break;default:qe(e,t),ft(t)}}function ft(t){var e=t.flags;if(e&2){try{e:{for(var n=t.return;n!==null;){if(rp(n)){var r=n;break e}n=n.return}throw Error(E(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(es(s,""),r.flags&=-33);var i=lf(t);ul(t,i,s);break;case 3:case 4:var a=r.stateNode.containerInfo,o=lf(t);ll(t,o,a);break;default:throw Error(E(161))}}catch(l){ee(t,t.return,l)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function i0(t,e,n){N=t,ap(t)}function ap(t,e,n){for(var r=(t.mode&1)!==0;N!==null;){var s=N,i=s.child;if(s.tag===22&&r){var a=s.memoizedState!==null||Js;if(!a){var o=s.alternate,l=o!==null&&o.memoizedState!==null||_e;o=Js;var u=_e;if(Js=a,(_e=l)&&!u)for(N=s;N!==null;)a=N,l=a.child,a.tag===22&&a.memoizedState!==null?df(s):l!==null?(l.return=a,N=l):df(s);for(;i!==null;)N=i,ap(i),i=i.sibling;N=s,Js=o,_e=u}cf(t)}else s.subtreeFlags&8772&&i!==null?(i.return=s,N=i):cf(t)}}function cf(t){for(;N!==null;){var e=N;if(e.flags&8772){var n=e.alternate;try{if(e.flags&8772)switch(e.tag){case 0:case 11:case 15:_e||pa(5,e);break;case 1:var r=e.stateNode;if(e.flags&4&&!_e)if(n===null)r.componentDidMount();else{var s=e.elementType===e.type?n.memoizedProps:et(e.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=e.updateQueue;i!==null&&Qc(e,i,r);break;case 3:var a=e.updateQueue;if(a!==null){if(n=null,e.child!==null)switch(e.child.tag){case 5:n=e.child.stateNode;break;case 1:n=e.child.stateNode}Qc(e,a,n)}break;case 5:var o=e.stateNode;if(n===null&&e.flags&4){n=o;var l=e.memoizedProps;switch(e.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(e.memoizedState===null){var u=e.alternate;if(u!==null){var h=u.memoizedState;if(h!==null){var d=h.dehydrated;d!==null&&ss(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(E(163))}_e||e.flags&512&&ol(e)}catch(c){ee(e,e.return,c)}}if(e===t){N=null;break}if(n=e.sibling,n!==null){n.return=e.return,N=n;break}N=e.return}}function ff(t){for(;N!==null;){var e=N;if(e===t){N=null;break}var n=e.sibling;if(n!==null){n.return=e.return,N=n;break}N=e.return}}function df(t){for(;N!==null;){var e=N;try{switch(e.tag){case 0:case 11:case 15:var n=e.return;try{pa(4,e)}catch(l){ee(e,n,l)}break;case 1:var r=e.stateNode;if(typeof r.componentDidMount=="function"){var s=e.return;try{r.componentDidMount()}catch(l){ee(e,s,l)}}var i=e.return;try{ol(e)}catch(l){ee(e,i,l)}break;case 5:var a=e.return;try{ol(e)}catch(l){ee(e,a,l)}}}catch(l){ee(e,e.return,l)}if(e===t){N=null;break}var o=e.sibling;if(o!==null){o.return=e.return,N=o;break}N=e.return}}var a0=Math.ceil,Ki=It.ReactCurrentDispatcher,hu=It.ReactCurrentOwner,Je=It.ReactCurrentBatchConfig,z=0,fe=null,ne=null,me=0,je=0,Xn=nn(0),ae=0,ms=null,kn=0,ma=0,pu=0,Jr=null,Le=null,mu=0,dr=1/0,kt=null,Qi=!1,cl=null,Xt=null,Xs=!1,Vt=null,Ji=0,Xr=0,fl=null,vi=-1,wi=0;function Ee(){return z&6?te():vi!==-1?vi:vi=te()}function Gt(t){return t.mode&1?z&2&&me!==0?me&-me:Vy.transition!==null?(wi===0&&(wi=Hd()),wi):(t=U,t!==0||(t=window.event,t=t===void 0?16:Yd(t.type)),t):1}function at(t,e,n,r){if(50<Xr)throw Xr=0,fl=null,Error(E(185));_s(t,n,r),(!(z&2)||t!==fe)&&(t===fe&&(!(z&2)&&(ma|=n),ae===4&&Ut(t,me)),be(t,r),n===1&&z===0&&!(e.mode&1)&&(dr=te()+500,fa&&rn()))}function be(t,e){var n=t.callbackNode;Vg(t,e);var r=Ai(t,t===fe?me:0);if(r===0)n!==null&&xc(n),t.callbackNode=null,t.callbackPriority=0;else if(e=r&-r,t.callbackPriority!==e){if(n!=null&&xc(n),e===1)t.tag===0?By(hf.bind(null,t)):gh(hf.bind(null,t)),jy(function(){!(z&6)&&rn()}),n=null;else{switch(Wd(r)){case 1:n=Ul;break;case 4:n=Bd;break;case 16:n=Oi;break;case 536870912:n=Vd;break;default:n=Oi}n=pp(n,op.bind(null,t))}t.callbackPriority=e,t.callbackNode=n}}function op(t,e){if(vi=-1,wi=0,z&6)throw Error(E(327));var n=t.callbackNode;if(nr()&&t.callbackNode!==n)return null;var r=Ai(t,t===fe?me:0);if(r===0)return null;if(r&30||r&t.expiredLanes||e)e=Xi(t,r);else{e=r;var s=z;z|=2;var i=up();(fe!==t||me!==e)&&(kt=null,dr=te()+500,yn(t,e));do try{u0();break}catch(o){lp(t,o)}while(!0);eu(),Ki.current=i,z=s,ne!==null?e=0:(fe=null,me=0,e=ae)}if(e!==0){if(e===2&&(s=jo(t),s!==0&&(r=s,e=dl(t,s))),e===1)throw n=ms,yn(t,0),Ut(t,r),be(t,te()),n;if(e===6)Ut(t,r);else{if(s=t.current.alternate,!(r&30)&&!o0(s)&&(e=Xi(t,r),e===2&&(i=jo(t),i!==0&&(r=i,e=dl(t,i))),e===1))throw n=ms,yn(t,0),Ut(t,r),be(t,te()),n;switch(t.finishedWork=s,t.finishedLanes=r,e){case 0:case 1:throw Error(E(345));case 2:fn(t,Le,kt);break;case 3:if(Ut(t,r),(r&130023424)===r&&(e=mu+500-te(),10<e)){if(Ai(t,0)!==0)break;if(s=t.suspendedLanes,(s&r)!==r){Ee(),t.pingedLanes|=t.suspendedLanes&s;break}t.timeoutHandle=Ko(fn.bind(null,t,Le,kt),e);break}fn(t,Le,kt);break;case 4:if(Ut(t,r),(r&4194240)===r)break;for(e=t.eventTimes,s=-1;0<r;){var a=31-it(r);i=1<<a,a=e[a],a>s&&(s=a),r&=~i}if(r=s,r=te()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*a0(r/1960))-r,10<r){t.timeoutHandle=Ko(fn.bind(null,t,Le,kt),r);break}fn(t,Le,kt);break;case 5:fn(t,Le,kt);break;default:throw Error(E(329))}}}return be(t,te()),t.callbackNode===n?op.bind(null,t):null}function dl(t,e){var n=Jr;return t.current.memoizedState.isDehydrated&&(yn(t,e).flags|=256),t=Xi(t,e),t!==2&&(e=Le,Le=n,e!==null&&hl(e)),t}function hl(t){Le===null?Le=t:Le.push.apply(Le,t)}function o0(t){for(var e=t;;){if(e.flags&16384){var n=e.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],i=s.getSnapshot;s=s.value;try{if(!ot(i(),s))return!1}catch{return!1}}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Ut(t,e){for(e&=~pu,e&=~ma,t.suspendedLanes|=e,t.pingedLanes&=~e,t=t.expirationTimes;0<e;){var n=31-it(e),r=1<<n;t[n]=-1,e&=~r}}function hf(t){if(z&6)throw Error(E(327));nr();var e=Ai(t,0);if(!(e&1))return be(t,te()),null;var n=Xi(t,e);if(t.tag!==0&&n===2){var r=jo(t);r!==0&&(e=r,n=dl(t,r))}if(n===1)throw n=ms,yn(t,0),Ut(t,e),be(t,te()),n;if(n===6)throw Error(E(345));return t.finishedWork=t.current.alternate,t.finishedLanes=e,fn(t,Le,kt),be(t,te()),null}function gu(t,e){var n=z;z|=1;try{return t(e)}finally{z=n,z===0&&(dr=te()+500,fa&&rn())}}function Cn(t){Vt!==null&&Vt.tag===0&&!(z&6)&&nr();var e=z;z|=1;var n=Je.transition,r=U;try{if(Je.transition=null,U=1,t)return t()}finally{U=r,Je.transition=n,z=e,!(z&6)&&rn()}}function yu(){je=Xn.current,K(Xn)}function yn(t,e){t.finishedWork=null,t.finishedLanes=0;var n=t.timeoutHandle;if(n!==-1&&(t.timeoutHandle=-1,My(n)),ne!==null)for(n=ne.return;n!==null;){var r=n;switch(Yl(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Fi();break;case 3:cr(),K(Ae),K(Se),au();break;case 5:iu(r);break;case 4:cr();break;case 13:K(G);break;case 19:K(G);break;case 10:tu(r.type._context);break;case 22:case 23:yu()}n=n.return}if(fe=t,ne=t=Yt(t.current,null),me=je=e,ae=0,ms=null,pu=ma=kn=0,Le=Jr=null,pn!==null){for(e=0;e<pn.length;e++)if(n=pn[e],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,i=n.pending;if(i!==null){var a=i.next;i.next=s,r.next=a}n.pending=r}pn=null}return t}function lp(t,e){do{var n=ne;try{if(eu(),mi.current=Wi,Hi){for(var r=Y.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}Hi=!1}if(Sn=0,ce=ie=Y=null,Kr=!1,ds=0,hu.current=null,n===null||n.return===null){ae=1,ms=e,ne=null;break}e:{var i=t,a=n.return,o=n,l=e;if(e=me,o.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,h=o,d=h.tag;if(!(h.mode&1)&&(d===0||d===11||d===15)){var c=h.alternate;c?(h.updateQueue=c.updateQueue,h.memoizedState=c.memoizedState,h.lanes=c.lanes):(h.updateQueue=null,h.memoizedState=null)}var g=Zc(a);if(g!==null){g.flags&=-257,ef(g,a,o,i,e),g.mode&1&&qc(i,u,e),e=g,l=u;var v=e.updateQueue;if(v===null){var w=new Set;w.add(l),e.updateQueue=w}else v.add(l);break e}else{if(!(e&1)){qc(i,u,e),vu();break e}l=Error(E(426))}}else if(J&&o.mode&1){var _=Zc(a);if(_!==null){!(_.flags&65536)&&(_.flags|=256),ef(_,a,o,i,e),ql(fr(l,o));break e}}i=l=fr(l,o),ae!==4&&(ae=2),Jr===null?Jr=[i]:Jr.push(i),i=a;do{switch(i.tag){case 3:i.flags|=65536,e&=-e,i.lanes|=e;var m=Wh(i,l,e);Kc(i,m);break e;case 1:o=l;var f=i.type,p=i.stateNode;if(!(i.flags&128)&&(typeof f.getDerivedStateFromError=="function"||p!==null&&typeof p.componentDidCatch=="function"&&(Xt===null||!Xt.has(p)))){i.flags|=65536,e&=-e,i.lanes|=e;var y=Kh(i,o,e);Kc(i,y);break e}}i=i.return}while(i!==null)}fp(n)}catch(k){e=k,ne===n&&n!==null&&(ne=n=n.return);continue}break}while(!0)}function up(){var t=Ki.current;return Ki.current=Wi,t===null?Wi:t}function vu(){(ae===0||ae===3||ae===2)&&(ae=4),fe===null||!(kn&268435455)&&!(ma&268435455)||Ut(fe,me)}function Xi(t,e){var n=z;z|=2;var r=up();(fe!==t||me!==e)&&(kt=null,yn(t,e));do try{l0();break}catch(s){lp(t,s)}while(!0);if(eu(),z=n,Ki.current=r,ne!==null)throw Error(E(261));return fe=null,me=0,ae}function l0(){for(;ne!==null;)cp(ne)}function u0(){for(;ne!==null&&!Tg();)cp(ne)}function cp(t){var e=hp(t.alternate,t,je);t.memoizedProps=t.pendingProps,e===null?fp(t):ne=e,hu.current=null}function fp(t){var e=t;do{var n=e.alternate;if(t=e.return,e.flags&32768){if(n=n0(n,e),n!==null){n.flags&=32767,ne=n;return}if(t!==null)t.flags|=32768,t.subtreeFlags=0,t.deletions=null;else{ae=6,ne=null;return}}else if(n=t0(n,e,je),n!==null){ne=n;return}if(e=e.sibling,e!==null){ne=e;return}ne=e=t}while(e!==null);ae===0&&(ae=5)}function fn(t,e,n){var r=U,s=Je.transition;try{Je.transition=null,U=1,c0(t,e,n,r)}finally{Je.transition=s,U=r}return null}function c0(t,e,n,r){do nr();while(Vt!==null);if(z&6)throw Error(E(327));n=t.finishedWork;var s=t.finishedLanes;if(n===null)return null;if(t.finishedWork=null,t.finishedLanes=0,n===t.current)throw Error(E(177));t.callbackNode=null,t.callbackPriority=0;var i=n.lanes|n.childLanes;if(Hg(t,i),t===fe&&(ne=fe=null,me=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Xs||(Xs=!0,pp(Oi,function(){return nr(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Je.transition,Je.transition=null;var a=U;U=1;var o=z;z|=4,hu.current=null,s0(t,n),ip(n,t),Oy(Ho),Ii=!!Vo,Ho=Vo=null,t.current=n,i0(n),$g(),z=o,U=a,Je.transition=i}else t.current=n;if(Xs&&(Xs=!1,Vt=t,Ji=s),i=t.pendingLanes,i===0&&(Xt=null),jg(n.stateNode),be(t,te()),e!==null)for(r=t.onRecoverableError,n=0;n<e.length;n++)s=e[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(Qi)throw Qi=!1,t=cl,cl=null,t;return Ji&1&&t.tag!==0&&nr(),i=t.pendingLanes,i&1?t===fl?Xr++:(Xr=0,fl=t):Xr=0,rn(),null}function nr(){if(Vt!==null){var t=Wd(Ji),e=Je.transition,n=U;try{if(Je.transition=null,U=16>t?16:t,Vt===null)var r=!1;else{if(t=Vt,Vt=null,Ji=0,z&6)throw Error(E(331));var s=z;for(z|=4,N=t.current;N!==null;){var i=N,a=i.child;if(N.flags&16){var o=i.deletions;if(o!==null){for(var l=0;l<o.length;l++){var u=o[l];for(N=u;N!==null;){var h=N;switch(h.tag){case 0:case 11:case 15:Qr(8,h,i)}var d=h.child;if(d!==null)d.return=h,N=d;else for(;N!==null;){h=N;var c=h.sibling,g=h.return;if(np(h),h===u){N=null;break}if(c!==null){c.return=g,N=c;break}N=g}}}var v=i.alternate;if(v!==null){var w=v.child;if(w!==null){v.child=null;do{var _=w.sibling;w.sibling=null,w=_}while(w!==null)}}N=i}}if(i.subtreeFlags&2064&&a!==null)a.return=i,N=a;else e:for(;N!==null;){if(i=N,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Qr(9,i,i.return)}var m=i.sibling;if(m!==null){m.return=i.return,N=m;break e}N=i.return}}var f=t.current;for(N=f;N!==null;){a=N;var p=a.child;if(a.subtreeFlags&2064&&p!==null)p.return=a,N=p;else e:for(a=f;N!==null;){if(o=N,o.flags&2048)try{switch(o.tag){case 0:case 11:case 15:pa(9,o)}}catch(k){ee(o,o.return,k)}if(o===a){N=null;break e}var y=o.sibling;if(y!==null){y.return=o.return,N=y;break e}N=o.return}}if(z=s,rn(),wt&&typeof wt.onPostCommitFiberRoot=="function")try{wt.onPostCommitFiberRoot(aa,t)}catch{}r=!0}return r}finally{U=n,Je.transition=e}}return!1}function pf(t,e,n){e=fr(n,e),e=Wh(t,e,1),t=Jt(t,e,1),e=Ee(),t!==null&&(_s(t,1,e),be(t,e))}function ee(t,e,n){if(t.tag===3)pf(t,t,n);else for(;e!==null;){if(e.tag===3){pf(e,t,n);break}else if(e.tag===1){var r=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Xt===null||!Xt.has(r))){t=fr(n,t),t=Kh(e,t,1),e=Jt(e,t,1),t=Ee(),e!==null&&(_s(e,1,t),be(e,t));break}}e=e.return}}function f0(t,e,n){var r=t.pingCache;r!==null&&r.delete(e),e=Ee(),t.pingedLanes|=t.suspendedLanes&n,fe===t&&(me&n)===n&&(ae===4||ae===3&&(me&130023424)===me&&500>te()-mu?yn(t,0):pu|=n),be(t,e)}function dp(t,e){e===0&&(t.mode&1?(e=Ds,Ds<<=1,!(Ds&130023424)&&(Ds=4194304)):e=1);var n=Ee();t=Ot(t,e),t!==null&&(_s(t,e,n),be(t,n))}function d0(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),dp(t,n)}function h0(t,e){var n=0;switch(t.tag){case 13:var r=t.stateNode,s=t.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=t.stateNode;break;default:throw Error(E(314))}r!==null&&r.delete(e),dp(t,n)}var hp;hp=function(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps||Ae.current)Oe=!0;else{if(!(t.lanes&n)&&!(e.flags&128))return Oe=!1,e0(t,e,n);Oe=!!(t.flags&131072)}else Oe=!1,J&&e.flags&1048576&&yh(e,Di,e.index);switch(e.lanes=0,e.tag){case 2:var r=e.type;yi(t,e),t=e.pendingProps;var s=or(e,Se.current);tr(e,n),s=lu(null,e,r,t,s,n);var i=uu();return e.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(e.tag=1,e.memoizedState=null,e.updateQueue=null,Ie(r)?(i=!0,Mi(e)):i=!1,e.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,ru(e),s.updater=ha,e.stateNode=s,s._reactInternals=e,Zo(e,r,t,n),e=nl(null,e,r,!0,i,n)):(e.tag=0,J&&i&&Gl(e),Ce(null,e,s,n),e=e.child),e;case 16:r=e.elementType;e:{switch(yi(t,e),t=e.pendingProps,s=r._init,r=s(r._payload),e.type=r,s=e.tag=m0(r),t=et(r,t),s){case 0:e=tl(null,e,r,t,n);break e;case 1:e=rf(null,e,r,t,n);break e;case 11:e=tf(null,e,r,t,n);break e;case 14:e=nf(null,e,r,et(r.type,t),n);break e}throw Error(E(306,r,""))}return e;case 0:return r=e.type,s=e.pendingProps,s=e.elementType===r?s:et(r,s),tl(t,e,r,s,n);case 1:return r=e.type,s=e.pendingProps,s=e.elementType===r?s:et(r,s),rf(t,e,r,s,n);case 3:e:{if(Gh(e),t===null)throw Error(E(387));r=e.pendingProps,i=e.memoizedState,s=i.element,kh(t,e),Bi(e,r,null,n);var a=e.memoizedState;if(r=a.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},e.updateQueue.baseState=i,e.memoizedState=i,e.flags&256){s=fr(Error(E(423)),e),e=sf(t,e,r,n,s);break e}else if(r!==s){s=fr(Error(E(424)),e),e=sf(t,e,r,n,s);break e}else for(De=Qt(e.stateNode.containerInfo.firstChild),ze=e,J=!0,rt=null,n=_h(e,null,r,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(lr(),r===s){e=At(t,e,n);break e}Ce(t,e,r,n)}e=e.child}return e;case 5:return Ch(e),t===null&&Go(e),r=e.type,s=e.pendingProps,i=t!==null?t.memoizedProps:null,a=s.children,Wo(r,s)?a=null:i!==null&&Wo(r,i)&&(e.flags|=32),Xh(t,e),Ce(t,e,a,n),e.child;case 6:return t===null&&Go(e),null;case 13:return Yh(t,e,n);case 4:return su(e,e.stateNode.containerInfo),r=e.pendingProps,t===null?e.child=ur(e,null,r,n):Ce(t,e,r,n),e.child;case 11:return r=e.type,s=e.pendingProps,s=e.elementType===r?s:et(r,s),tf(t,e,r,s,n);case 7:return Ce(t,e,e.pendingProps,n),e.child;case 8:return Ce(t,e,e.pendingProps.children,n),e.child;case 12:return Ce(t,e,e.pendingProps.children,n),e.child;case 10:e:{if(r=e.type._context,s=e.pendingProps,i=e.memoizedProps,a=s.value,H(zi,r._currentValue),r._currentValue=a,i!==null)if(ot(i.value,a)){if(i.children===s.children&&!Ae.current){e=At(t,e,n);break e}}else for(i=e.child,i!==null&&(i.return=e);i!==null;){var o=i.dependencies;if(o!==null){a=i.child;for(var l=o.firstContext;l!==null;){if(l.context===r){if(i.tag===1){l=Rt(-1,n&-n),l.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var h=u.pending;h===null?l.next=l:(l.next=h.next,h.next=l),u.pending=l}}i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),Yo(i.return,n,e),o.lanes|=n;break}l=l.next}}else if(i.tag===10)a=i.type===e.type?null:i.child;else if(i.tag===18){if(a=i.return,a===null)throw Error(E(341));a.lanes|=n,o=a.alternate,o!==null&&(o.lanes|=n),Yo(a,n,e),a=i.sibling}else a=i.child;if(a!==null)a.return=i;else for(a=i;a!==null;){if(a===e){a=null;break}if(i=a.sibling,i!==null){i.return=a.return,a=i;break}a=a.return}i=a}Ce(t,e,s.children,n),e=e.child}return e;case 9:return s=e.type,r=e.pendingProps.children,tr(e,n),s=Xe(s),r=r(s),e.flags|=1,Ce(t,e,r,n),e.child;case 14:return r=e.type,s=et(r,e.pendingProps),s=et(r.type,s),nf(t,e,r,s,n);case 15:return Qh(t,e,e.type,e.pendingProps,n);case 17:return r=e.type,s=e.pendingProps,s=e.elementType===r?s:et(r,s),yi(t,e),e.tag=1,Ie(r)?(t=!0,Mi(e)):t=!1,tr(e,n),Hh(e,r,s),Zo(e,r,s,n),nl(null,e,r,!0,t,n);case 19:return qh(t,e,n);case 22:return Jh(t,e,n)}throw Error(E(156,e.tag))};function pp(t,e){return Ud(t,e)}function p0(t,e,n,r){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ke(t,e,n,r){return new p0(t,e,n,r)}function wu(t){return t=t.prototype,!(!t||!t.isReactComponent)}function m0(t){if(typeof t=="function")return wu(t)?1:0;if(t!=null){if(t=t.$$typeof,t===jl)return 11;if(t===Dl)return 14}return 2}function Yt(t,e){var n=t.alternate;return n===null?(n=Ke(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&14680064,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n}function xi(t,e,n,r,s,i){var a=2;if(r=t,typeof t=="function")wu(t)&&(a=1);else if(typeof t=="string")a=5;else e:switch(t){case Dn:return vn(n.children,s,i,e);case Ml:a=8,s|=8;break;case ko:return t=Ke(12,n,e,s|2),t.elementType=ko,t.lanes=i,t;case Co:return t=Ke(13,n,e,s),t.elementType=Co,t.lanes=i,t;case Eo:return t=Ke(19,n,e,s),t.elementType=Eo,t.lanes=i,t;case Cd:return ga(n,s,i,e);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case Sd:a=10;break e;case kd:a=9;break e;case jl:a=11;break e;case Dl:a=14;break e;case jt:a=16,r=null;break e}throw Error(E(130,t==null?t:typeof t,""))}return e=Ke(a,n,e,s),e.elementType=t,e.type=r,e.lanes=i,e}function vn(t,e,n,r){return t=Ke(7,t,r,e),t.lanes=n,t}function ga(t,e,n,r){return t=Ke(22,t,r,e),t.elementType=Cd,t.lanes=n,t.stateNode={isHidden:!1},t}function oo(t,e,n){return t=Ke(6,t,null,e),t.lanes=n,t}function lo(t,e,n){return e=Ke(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}function g0(t,e,n,r,s){this.tag=e,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ba(0),this.expirationTimes=Ba(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ba(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function xu(t,e,n,r,s,i,a,o,l){return t=new g0(t,e,n,o,l),e===1?(e=1,i===!0&&(e|=8)):e=0,i=Ke(3,null,null,e),t.current=i,i.stateNode=t,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},ru(i),t}function y0(t,e,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:jn,key:r==null?null:""+r,children:t,containerInfo:e,implementation:n}}function mp(t){if(!t)return en;t=t._reactInternals;e:{if(Rn(t)!==t||t.tag!==1)throw Error(E(170));var e=t;do{switch(e.tag){case 3:e=e.stateNode.context;break e;case 1:if(Ie(e.type)){e=e.stateNode.__reactInternalMemoizedMergedChildContext;break e}}e=e.return}while(e!==null);throw Error(E(171))}if(t.tag===1){var n=t.type;if(Ie(n))return mh(t,n,e)}return e}function gp(t,e,n,r,s,i,a,o,l){return t=xu(n,r,!0,t,s,i,a,o,l),t.context=mp(null),n=t.current,r=Ee(),s=Gt(n),i=Rt(r,s),i.callback=e??null,Jt(n,i,s),t.current.lanes=s,_s(t,s,r),be(t,r),t}function ya(t,e,n,r){var s=e.current,i=Ee(),a=Gt(s);return n=mp(n),e.context===null?e.context=n:e.pendingContext=n,e=Rt(i,a),e.payload={element:t},r=r===void 0?null:r,r!==null&&(e.callback=r),t=Jt(s,e,a),t!==null&&(at(t,s,a,i),pi(t,s,a)),a}function Gi(t){if(t=t.current,!t.child)return null;switch(t.child.tag){case 5:return t.child.stateNode;default:return t.child.stateNode}}function mf(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function _u(t,e){mf(t,e),(t=t.alternate)&&mf(t,e)}function v0(){return null}var yp=typeof reportError=="function"?reportError:function(t){console.error(t)};function Su(t){this._internalRoot=t}va.prototype.render=Su.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(E(409));ya(t,e,null,null)};va.prototype.unmount=Su.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Cn(function(){ya(null,t,null,null)}),e[Lt]=null}};function va(t){this._internalRoot=t}va.prototype.unstable_scheduleHydration=function(t){if(t){var e=Jd();t={blockedOn:null,target:t,priority:e};for(var n=0;n<zt.length&&e!==0&&e<zt[n].priority;n++);zt.splice(n,0,t),n===0&&Gd(t)}};function ku(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function wa(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11&&(t.nodeType!==8||t.nodeValue!==" react-mount-point-unstable "))}function gf(){}function w0(t,e,n,r,s){if(s){if(typeof r=="function"){var i=r;r=function(){var u=Gi(a);i.call(u)}}var a=gp(e,r,t,0,null,!1,!1,"",gf);return t._reactRootContainer=a,t[Lt]=a.current,os(t.nodeType===8?t.parentNode:t),Cn(),a}for(;s=t.lastChild;)t.removeChild(s);if(typeof r=="function"){var o=r;r=function(){var u=Gi(l);o.call(u)}}var l=xu(t,0,!1,null,null,!1,!1,"",gf);return t._reactRootContainer=l,t[Lt]=l.current,os(t.nodeType===8?t.parentNode:t),Cn(function(){ya(e,l,n,r)}),l}function xa(t,e,n,r,s){var i=n._reactRootContainer;if(i){var a=i;if(typeof s=="function"){var o=s;s=function(){var l=Gi(a);o.call(l)}}ya(e,a,t,s)}else a=w0(n,e,t,s,r);return Gi(a)}Kd=function(t){switch(t.tag){case 3:var e=t.stateNode;if(e.current.memoizedState.isDehydrated){var n=br(e.pendingLanes);n!==0&&(Bl(e,n|1),be(e,te()),!(z&6)&&(dr=te()+500,rn()))}break;case 13:Cn(function(){var r=Ot(t,1);if(r!==null){var s=Ee();at(r,t,1,s)}}),_u(t,1)}};Vl=function(t){if(t.tag===13){var e=Ot(t,134217728);if(e!==null){var n=Ee();at(e,t,134217728,n)}_u(t,134217728)}};Qd=function(t){if(t.tag===13){var e=Gt(t),n=Ot(t,e);if(n!==null){var r=Ee();at(n,t,e,r)}_u(t,e)}};Jd=function(){return U};Xd=function(t,e){var n=U;try{return U=t,e()}finally{U=n}};$o=function(t,e,n){switch(e){case"input":if(No(t,n),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+e)+'][type="radio"]'),e=0;e<n.length;e++){var r=n[e];if(r!==t&&r.form===t.form){var s=ca(r);if(!s)throw Error(E(90));Pd(r),No(r,s)}}}break;case"textarea":Nd(t,n);break;case"select":e=n.value,e!=null&&Yn(t,!!n.multiple,e,!1)}};$d=gu;Fd=Cn;var x0={usingClientEntryPoint:!1,Events:[ks,Vn,ca,bd,Td,gu]},Rr={findFiberByHostInstance:hn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},_0={bundleType:Rr.bundleType,version:Rr.version,rendererPackageName:Rr.rendererPackageName,rendererConfig:Rr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:It.ReactCurrentDispatcher,findHostInstanceByFiber:function(t){return t=Dd(t),t===null?null:t.stateNode},findFiberByHostInstance:Rr.findFiberByHostInstance||v0,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Gs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Gs.isDisabled&&Gs.supportsFiber)try{aa=Gs.inject(_0),wt=Gs}catch{}}Be.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=x0;Be.createPortal=function(t,e){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ku(e))throw Error(E(200));return y0(t,e,null,n)};Be.createRoot=function(t,e){if(!ku(t))throw Error(E(299));var n=!1,r="",s=yp;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(r=e.identifierPrefix),e.onRecoverableError!==void 0&&(s=e.onRecoverableError)),e=xu(t,1,!1,null,null,n,!1,r,s),t[Lt]=e.current,os(t.nodeType===8?t.parentNode:t),new Su(e)};Be.findDOMNode=function(t){if(t==null)return null;if(t.nodeType===1)return t;var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(E(188)):(t=Object.keys(t).join(","),Error(E(268,t)));return t=Dd(e),t=t===null?null:t.stateNode,t};Be.flushSync=function(t){return Cn(t)};Be.hydrate=function(t,e,n){if(!wa(e))throw Error(E(200));return xa(null,t,e,!0,n)};Be.hydrateRoot=function(t,e,n){if(!ku(t))throw Error(E(405));var r=n!=null&&n.hydratedSources||null,s=!1,i="",a=yp;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),e=gp(e,null,t,1,n??null,s,!1,i,a),t[Lt]=e.current,os(t),r)for(t=0;t<r.length;t++)n=r[t],s=n._getVersion,s=s(n._source),e.mutableSourceEagerHydrationData==null?e.mutableSourceEagerHydrationData=[n,s]:e.mutableSourceEagerHydrationData.push(n,s);return new va(e)};Be.render=function(t,e,n){if(!wa(e))throw Error(E(200));return xa(null,t,e,!1,n)};Be.unmountComponentAtNode=function(t){if(!wa(t))throw Error(E(40));return t._reactRootContainer?(Cn(function(){xa(null,null,t,!1,function(){t._reactRootContainer=null,t[Lt]=null})}),!0):!1};Be.unstable_batchedUpdates=gu;Be.unstable_renderSubtreeIntoContainer=function(t,e,n,r){if(!wa(n))throw Error(E(200));if(t==null||t._reactInternals===void 0)throw Error(E(38));return xa(t,e,n,!1,r)};Be.version="18.3.1-next-f1338f8080-20240426";function vp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(vp)}catch(t){console.error(t)}}vp(),vd.exports=Be;var S0=vd.exports,wp,yf=S0;wp=yf.createRoot,yf.hydrateRoot;const vf=[{id:"1",title:"The Missing Heirloom",background:"The wealthy Thompson family has reported the theft of a valuable diamond necklace, a family heirloom passed down for generations. The theft occurred during their annual garden party, where only family members and close friends were present.",clues:["The security cameras were mysteriously disabled between 7:00 PM and 7:15 PM.","A garden glove with soil stains was found near the safe.","Mrs. Thompson's niece, Emily, was seen arguing with her aunt about inheritance matters earlier that day.","The family butler, Jenkins, has recently been making expensive purchases despite his modest salary.","The safe showed no signs of forced entry, suggesting the thief knew the combination."],objective:"Identify who stole the Thompson family's diamond necklace and how they did it.",characters:[{name:"Mrs. Eleanor Thompson",description:"The 68-year-old family matriarch and owner of the necklace. She is known to be forgetful lately."},{name:"Jenkins",description:"The family butler who has served the Thompsons for 25 years. Recently purchased a new car.",isGuilty:!1},{name:"Emily Thompson",description:"Mrs. Thompson's 32-year-old niece who was recently excluded from the will.",isGuilty:!0},{name:"Gerald Thompson",description:"Mrs. Thompson's son who manages the family finances and knows the safe combination."}]},{id:"2",title:"Murder at the Art Gallery",background:"Famous art curator Dr. Richard Bell was found dead in his gallery the morning after a high-profile exhibition opening. The cause of death appears to be blunt force trauma to the head. Four suspects were present at the gallery after hours.",clues:["A bloodied sculpture was found near the body, likely the murder weapon.","The gallery's assistant, Ms. Parker, claims she saw Dr. Bell arguing with artist Marcus Reid over the valuation of his work.","The security log shows that wealthy collector Victoria Hartman was the last to leave the gallery at 11:45 PM.",`Dr. Bell's notebook contained a cryptic message: "Exposed fraud must be addressed tonight."`,"The gallery's financial records show discrepancies that Dr. Bell had recently discovered."],objective:"Determine who killed Dr. Richard Bell and their motive.",characters:[{name:"Marcus Reid",description:"An up-and-coming artist whose work was featured in the exhibition. Has a temper and was arguing with the victim.",isGuilty:!1},{name:"Ms. Parker",description:"The gallery assistant who has worked with Dr. Bell for 5 years. She was responsible for the gallery's financial records.",isGuilty:!0},{name:"Victoria Hartman",description:"A wealthy art collector who was interested in acquiring several pieces from the exhibition.",isGuilty:!1},{name:"James Wilson",description:"The gallery's security guard who was supposedly making rounds during the time of the murder."}]},{id:"3",title:"Corporate Espionage",background:"Tech startup Quantum Innovations is about to launch a revolutionary product, but their prototype and research files have gone missing. The CEO suspects it's an inside job, as only key team members had access to the secure lab.",clues:["The access logs show that lead engineer David Chen entered the lab at 10:30 PM, though he claims he was at home.","Marketing director Sarah Lopez recently received several large bank deposits from an unknown source.","The company's main competitor, TechGiant, announced a similar product just days after the theft.",'Security cameras were disabled for "maintenance" the night of the theft, authorized by IT manager Raj Patel.',"The janitor reports seeing CFO Michael Warren working unusually late that night."],objective:"Discover who stole Quantum Innovations' prototype and research, and determine if corporate espionage was involved.",characters:[{name:"David Chen",description:"Lead engineer who designed much of the prototype. His access card was used the night of the theft.",isGuilty:!1},{name:"Sarah Lopez",description:"Marketing director who has access to all product details for promotional planning.",isGuilty:!1},{name:"Raj Patel",description:"IT manager who authorized the security camera maintenance and has been with the company for only 6 months.",isGuilty:!0},{name:"Michael Warren",description:"CFO who has been pushing for an earlier product launch to satisfy investors."}]}],k0=()=>{const t=Math.floor(Math.random()*vf.length);return vf[t]},pl="RFC3986",ml={RFC1738:t=>String(t).replace(/%20/g,"+"),RFC3986:t=>String(t)},C0="RFC1738",E0=Array.isArray,dt=(()=>{const t=[];for(let e=0;e<256;++e)t.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return t})(),uo=1024,P0=(t,e,n,r,s)=>{if(t.length===0)return t;let i=t;if(typeof t=="symbol"?i=Symbol.prototype.toString.call(t):typeof t!="string"&&(i=String(t)),n==="iso-8859-1")return escape(i).replace(/%u[0-9a-f]{4}/gi,function(o){return"%26%23"+parseInt(o.slice(2),16)+"%3B"});let a="";for(let o=0;o<i.length;o+=uo){const l=i.length>=uo?i.slice(o,o+uo):i,u=[];for(let h=0;h<l.length;++h){let d=l.charCodeAt(h);if(d===45||d===46||d===95||d===126||d>=48&&d<=57||d>=65&&d<=90||d>=97&&d<=122||s===C0&&(d===40||d===41)){u[u.length]=l.charAt(h);continue}if(d<128){u[u.length]=dt[d];continue}if(d<2048){u[u.length]=dt[192|d>>6]+dt[128|d&63];continue}if(d<55296||d>=57344){u[u.length]=dt[224|d>>12]+dt[128|d>>6&63]+dt[128|d&63];continue}h+=1,d=65536+((d&1023)<<10|l.charCodeAt(h)&1023),u[u.length]=dt[240|d>>18]+dt[128|d>>12&63]+dt[128|d>>6&63]+dt[128|d&63]}a+=u.join("")}return a};function R0(t){return!t||typeof t!="object"?!1:!!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t))}function wf(t,e){if(E0(t)){const n=[];for(let r=0;r<t.length;r+=1)n.push(e(t[r]));return n}return e(t)}const N0=Object.prototype.hasOwnProperty,xp={brackets(t){return String(t)+"[]"},comma:"comma",indices(t,e){return String(t)+"["+e+"]"},repeat(t){return String(t)}},pt=Array.isArray,L0=Array.prototype.push,_p=function(t,e){L0.apply(t,pt(e)?e:[e])},O0=Date.prototype.toISOString,se={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:P0,encodeValuesOnly:!1,format:pl,formatter:ml[pl],indices:!1,serializeDate(t){return O0.call(t)},skipNulls:!1,strictNullHandling:!1};function A0(t){return typeof t=="string"||typeof t=="number"||typeof t=="boolean"||typeof t=="symbol"||typeof t=="bigint"}const co={};function Sp(t,e,n,r,s,i,a,o,l,u,h,d,c,g,v,w,_,m){let f=t,p=m,y=0,k=!1;for(;(p=p.get(co))!==void 0&&!k;){const L=p.get(t);if(y+=1,typeof L<"u"){if(L===y)throw new RangeError("Cyclic object value");k=!0}typeof p.get(co)>"u"&&(y=0)}if(typeof u=="function"?f=u(e,f):f instanceof Date?f=c==null?void 0:c(f):n==="comma"&&pt(f)&&(f=wf(f,function(L){return L instanceof Date?c==null?void 0:c(L):L})),f===null){if(i)return l&&!w?l(e,se.encoder,_,"key",g):e;f=""}if(A0(f)||R0(f)){if(l){const L=w?e:l(e,se.encoder,_,"key",g);return[(v==null?void 0:v(L))+"="+(v==null?void 0:v(l(f,se.encoder,_,"value",g)))]}return[(v==null?void 0:v(e))+"="+(v==null?void 0:v(String(f)))]}const S=[];if(typeof f>"u")return S;let x;if(n==="comma"&&pt(f))w&&l&&(f=wf(f,l)),x=[{value:f.length>0?f.join(",")||null:void 0}];else if(pt(u))x=u;else{const L=Object.keys(f);x=h?L.sort(h):L}const R=o?String(e).replace(/\./g,"%2E"):String(e),F=r&&pt(f)&&f.length===1?R+"[]":R;if(s&&pt(f)&&f.length===0)return F+"[]";for(let L=0;L<x.length;++L){const T=x[L],Te=typeof T=="object"&&typeof T.value<"u"?T.value:f[T];if(a&&Te===null)continue;const Ye=d&&o?T.replace(/\./g,"%2E"):T,ut=pt(f)?typeof n=="function"?n(F,Ye):F:F+(d?"."+Ye:"["+Ye+"]");m.set(t,y);const ct=new WeakMap;ct.set(co,m),_p(S,Sp(Te,ut,n,r,s,i,a,o,n==="comma"&&w&&pt(f)?null:l,u,h,d,c,g,v,w,_,ct))}return S}function I0(t=se){if(typeof t.allowEmptyArrays<"u"&&typeof t.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof t.encodeDotInKeys<"u"&&typeof t.encodeDotInKeys!="boolean")throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(t.encoder!==null&&typeof t.encoder<"u"&&typeof t.encoder!="function")throw new TypeError("Encoder has to be a function.");const e=t.charset||se.charset;if(typeof t.charset<"u"&&t.charset!=="utf-8"&&t.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let n=pl;if(typeof t.format<"u"){if(!N0.call(ml,t.format))throw new TypeError("Unknown format option provided.");n=t.format}const r=ml[n];let s=se.filter;(typeof t.filter=="function"||pt(t.filter))&&(s=t.filter);let i;if(t.arrayFormat&&t.arrayFormat in xp?i=t.arrayFormat:"indices"in t?i=t.indices?"indices":"repeat":i=se.arrayFormat,"commaRoundTrip"in t&&typeof t.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");const a=typeof t.allowDots>"u"?t.encodeDotInKeys?!0:se.allowDots:!!t.allowDots;return{addQueryPrefix:typeof t.addQueryPrefix=="boolean"?t.addQueryPrefix:se.addQueryPrefix,allowDots:a,allowEmptyArrays:typeof t.allowEmptyArrays=="boolean"?!!t.allowEmptyArrays:se.allowEmptyArrays,arrayFormat:i,charset:e,charsetSentinel:typeof t.charsetSentinel=="boolean"?t.charsetSentinel:se.charsetSentinel,commaRoundTrip:!!t.commaRoundTrip,delimiter:typeof t.delimiter>"u"?se.delimiter:t.delimiter,encode:typeof t.encode=="boolean"?t.encode:se.encode,encodeDotInKeys:typeof t.encodeDotInKeys=="boolean"?t.encodeDotInKeys:se.encodeDotInKeys,encoder:typeof t.encoder=="function"?t.encoder:se.encoder,encodeValuesOnly:typeof t.encodeValuesOnly=="boolean"?t.encodeValuesOnly:se.encodeValuesOnly,filter:s,format:n,formatter:r,serializeDate:typeof t.serializeDate=="function"?t.serializeDate:se.serializeDate,skipNulls:typeof t.skipNulls=="boolean"?t.skipNulls:se.skipNulls,sort:typeof t.sort=="function"?t.sort:null,strictNullHandling:typeof t.strictNullHandling=="boolean"?t.strictNullHandling:se.strictNullHandling}}function b0(t,e={}){let n=t;const r=I0(e);let s,i;typeof r.filter=="function"?(i=r.filter,n=i("",n)):pt(r.filter)&&(i=r.filter,s=i);const a=[];if(typeof n!="object"||n===null)return"";const o=xp[r.arrayFormat],l=o==="comma"&&r.commaRoundTrip;s||(s=Object.keys(n)),r.sort&&s.sort(r.sort);const u=new WeakMap;for(let c=0;c<s.length;++c){const g=s[c];r.skipNulls&&n[g]===null||_p(a,Sp(n[g],g,o,l,r.allowEmptyArrays,r.strictNullHandling,r.skipNulls,r.encodeDotInKeys,r.encode?r.encoder:null,r.filter,r.sort,r.allowDots,r.serializeDate,r.format,r.formatter,r.encodeValuesOnly,r.charset,u))}const h=a.join(r.delimiter);let d=r.addQueryPrefix===!0?"?":"";return r.charsetSentinel&&(r.charset==="iso-8859-1"?d+="utf8=%26%2310003%3B&":d+="utf8=%E2%9C%93&"),h.length>0?d+h:""}const Fn="4.104.0";let xf=!1,Gr,kp,Cp,gl,Ep,Pp,Rp,Np,Lp;function T0(t,e={auto:!1}){if(xf)throw new Error(`you must \`import 'openai/shims/${t.kind}'\` before importing anything else from openai`);if(Gr)throw new Error(`can't \`import 'openai/shims/${t.kind}'\` after \`import 'openai/shims/${Gr}'\``);xf=e.auto,Gr=t.kind,kp=t.fetch,Cp=t.FormData,gl=t.File,Ep=t.ReadableStream,Pp=t.getMultipartRequestOptions,Rp=t.getDefaultAgent,Np=t.fileFromPath,Lp=t.isFsReadStream}class $0{constructor(e){this.body=e}get[Symbol.toStringTag](){return"MultipartBody"}}function F0({manuallyImported:t}={}){const e=t?"You may need to use polyfills":"Add one of these imports before your first `import … from 'openai'`:\n- `import 'openai/shims/node'` (if you're running on Node)\n- `import 'openai/shims/web'` (otherwise)\n";let n,r,s,i;try{n=fetch,r=Request,s=Response,i=Headers}catch(a){throw new Error(`this environment is missing the following Web Fetch API type: ${a.message}. ${e}`)}return{kind:"web",fetch:n,Request:r,Response:s,Headers:i,FormData:typeof FormData<"u"?FormData:class{constructor(){throw new Error(`file uploads aren't supported in this environment yet as 'FormData' is undefined. ${e}`)}},Blob:typeof Blob<"u"?Blob:class{constructor(){throw new Error(`file uploads aren't supported in this environment yet as 'Blob' is undefined. ${e}`)}},File:typeof File<"u"?File:class{constructor(){throw new Error(`file uploads aren't supported in this environment yet as 'File' is undefined. ${e}`)}},ReadableStream:typeof ReadableStream<"u"?ReadableStream:class{constructor(){throw new Error(`streaming isn't supported in this environment yet as 'ReadableStream' is undefined. ${e}`)}},getMultipartRequestOptions:async(a,o)=>({...o,body:new $0(a)}),getDefaultAgent:a=>{},fileFromPath:()=>{throw new Error("The `fileFromPath` function is only supported in Node. See the README for more details: https://www.github.com/openai/openai-node#file-uploads")},isFsReadStream:a=>!1}}const Op=()=>{Gr||T0(F0(),{auto:!0})};Op();class I extends Error{}class ye extends I{constructor(e,n,r,s){super(`${ye.makeMessage(e,n,r)}`),this.status=e,this.headers=s,this.request_id=s==null?void 0:s["x-request-id"],this.error=n;const i=n;this.code=i==null?void 0:i.code,this.param=i==null?void 0:i.param,this.type=i==null?void 0:i.type}static makeMessage(e,n,r){const s=n!=null&&n.message?typeof n.message=="string"?n.message:JSON.stringify(n.message):n?JSON.stringify(n):r;return e&&s?`${e} ${s}`:e?`${e} status code (no body)`:s||"(no status code or body)"}static generate(e,n,r,s){if(!e||!s)return new _a({message:r,cause:vl(n)});const i=n==null?void 0:n.error;return e===400?new Ap(e,i,r,s):e===401?new Ip(e,i,r,s):e===403?new bp(e,i,r,s):e===404?new Tp(e,i,r,s):e===409?new $p(e,i,r,s):e===422?new Fp(e,i,r,s):e===429?new Mp(e,i,r,s):e>=500?new jp(e,i,r,s):new ye(e,i,r,s)}}class Qe extends ye{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class _a extends ye{constructor({message:e,cause:n}){super(void 0,void 0,e||"Connection error.",void 0),n&&(this.cause=n)}}class Cu extends _a{constructor({message:e}={}){super({message:e??"Request timed out."})}}class Ap extends ye{}class Ip extends ye{}class bp extends ye{}class Tp extends ye{}class $p extends ye{}class Fp extends ye{}class Mp extends ye{}class jp extends ye{}class Dp extends I{constructor(){super("Could not parse response content as the length limit was reached")}}class zp extends I{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}var Ys=function(t,e,n,r,s){if(r==="m")throw new TypeError("Private method is not writable");if(r==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?t!==e||!s:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return r==="a"?s.call(t,n):s?s.value=n:e.set(t,n),n},un=function(t,e,n,r){if(n==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?t!==e||!r:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?r:n==="a"?r.call(t):r?r.value:e.get(t)},Fe;class Sa{constructor(){Fe.set(this,void 0),this.buffer=new Uint8Array,Ys(this,Fe,null,"f")}decode(e){if(e==null)return[];const n=e instanceof ArrayBuffer?new Uint8Array(e):typeof e=="string"?new TextEncoder().encode(e):e;let r=new Uint8Array(this.buffer.length+n.length);r.set(this.buffer),r.set(n,this.buffer.length),this.buffer=r;const s=[];let i;for(;(i=M0(this.buffer,un(this,Fe,"f")))!=null;){if(i.carriage&&un(this,Fe,"f")==null){Ys(this,Fe,i.index,"f");continue}if(un(this,Fe,"f")!=null&&(i.index!==un(this,Fe,"f")+1||i.carriage)){s.push(this.decodeText(this.buffer.slice(0,un(this,Fe,"f")-1))),this.buffer=this.buffer.slice(un(this,Fe,"f")),Ys(this,Fe,null,"f");continue}const a=un(this,Fe,"f")!==null?i.preceding-1:i.preceding,o=this.decodeText(this.buffer.slice(0,a));s.push(o),this.buffer=this.buffer.slice(i.index),Ys(this,Fe,null,"f")}return s}decodeText(e){if(e==null)return"";if(typeof e=="string")return e;if(typeof Buffer<"u"){if(e instanceof Buffer)return e.toString();if(e instanceof Uint8Array)return Buffer.from(e).toString();throw new I(`Unexpected: received non-Uint8Array (${e.constructor.name}) stream chunk in an environment with a global "Buffer" defined, which this library assumes to be Node. Please report this error.`)}if(typeof TextDecoder<"u"){if(e instanceof Uint8Array||e instanceof ArrayBuffer)return this.textDecoder??(this.textDecoder=new TextDecoder("utf8")),this.textDecoder.decode(e);throw new I(`Unexpected: received non-Uint8Array/ArrayBuffer (${e.constructor.name}) in a web platform. Please report this error.`)}throw new I("Unexpected: neither Buffer nor TextDecoder are available as globals. Please report this error.")}flush(){return this.buffer.length?this.decode(`
`):[]}}Fe=new WeakMap;Sa.NEWLINE_CHARS=new Set([`
`,"\r"]);Sa.NEWLINE_REGEXP=/\r\n|[\n\r]/g;function M0(t,e){for(let s=e??0;s<t.length;s++){if(t[s]===10)return{preceding:s,index:s+1,carriage:!1};if(t[s]===13)return{preceding:s,index:s+1,carriage:!0}}return null}function j0(t){for(let r=0;r<t.length-1;r++){if(t[r]===10&&t[r+1]===10||t[r]===13&&t[r+1]===13)return r+2;if(t[r]===13&&t[r+1]===10&&r+3<t.length&&t[r+2]===13&&t[r+3]===10)return r+4}return-1}function Up(t){if(t[Symbol.asyncIterator])return t;const e=t.getReader();return{async next(){try{const n=await e.read();return n!=null&&n.done&&e.releaseLock(),n}catch(n){throw e.releaseLock(),n}},async return(){const n=e.cancel();return e.releaseLock(),await n,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}class yt{constructor(e,n){this.iterator=e,this.controller=n}static fromSSEResponse(e,n){let r=!1;async function*s(){if(r)throw new Error("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let i=!1;try{for await(const a of D0(e,n))if(!i){if(a.data.startsWith("[DONE]")){i=!0;continue}if(a.event===null||a.event.startsWith("response.")||a.event.startsWith("transcript.")){let o;try{o=JSON.parse(a.data)}catch(l){throw console.error("Could not parse message into JSON:",a.data),console.error("From chunk:",a.raw),l}if(o&&o.error)throw new ye(void 0,o.error,void 0,Jp(e.headers));yield o}else{let o;try{o=JSON.parse(a.data)}catch(l){throw console.error("Could not parse message into JSON:",a.data),console.error("From chunk:",a.raw),l}if(a.event=="error")throw new ye(void 0,o.error,o.message,void 0);yield{event:a.event,data:o}}}i=!0}catch(a){if(a instanceof Error&&a.name==="AbortError")return;throw a}finally{i||n.abort()}}return new yt(s,n)}static fromReadableStream(e,n){let r=!1;async function*s(){const a=new Sa,o=Up(e);for await(const l of o)for(const u of a.decode(l))yield u;for(const l of a.flush())yield l}async function*i(){if(r)throw new Error("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let a=!1;try{for await(const o of s())a||o&&(yield JSON.parse(o));a=!0}catch(o){if(o instanceof Error&&o.name==="AbortError")return;throw o}finally{a||n.abort()}}return new yt(i,n)}[Symbol.asyncIterator](){return this.iterator()}tee(){const e=[],n=[],r=this.iterator(),s=i=>({next:()=>{if(i.length===0){const a=r.next();e.push(a),n.push(a)}return i.shift()}});return[new yt(()=>s(e),this.controller),new yt(()=>s(n),this.controller)]}toReadableStream(){const e=this;let n;const r=new TextEncoder;return new Ep({async start(){n=e[Symbol.asyncIterator]()},async pull(s){try{const{value:i,done:a}=await n.next();if(a)return s.close();const o=r.encode(JSON.stringify(i)+`
`);s.enqueue(o)}catch(i){s.error(i)}},async cancel(){var s;await((s=n.return)==null?void 0:s.call(n))}})}}async function*D0(t,e){if(!t.body)throw e.abort(),new I("Attempted to iterate over a response with no body");const n=new U0,r=new Sa,s=Up(t.body);for await(const i of z0(s))for(const a of r.decode(i)){const o=n.decode(a);o&&(yield o)}for(const i of r.flush()){const a=n.decode(i);a&&(yield a)}}async function*z0(t){let e=new Uint8Array;for await(const n of t){if(n==null)continue;const r=n instanceof ArrayBuffer?new Uint8Array(n):typeof n=="string"?new TextEncoder().encode(n):n;let s=new Uint8Array(e.length+r.length);s.set(e),s.set(r,e.length),e=s;let i;for(;(i=j0(e))!==-1;)yield e.slice(0,i),e=e.slice(i)}e.length>0&&(yield e)}class U0{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;const i={event:this.event,data:this.data.join(`
`),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],i}if(this.chunks.push(e),e.startsWith(":"))return null;let[n,r,s]=B0(e,":");return s.startsWith(" ")&&(s=s.substring(1)),n==="event"?this.event=s:n==="data"&&this.data.push(s),null}}function B0(t,e){const n=t.indexOf(e);return n!==-1?[t.substring(0,n),e,t.substring(n+e.length)]:[t,"",""]}const Bp=t=>t!=null&&typeof t=="object"&&typeof t.url=="string"&&typeof t.blob=="function",Vp=t=>t!=null&&typeof t=="object"&&typeof t.name=="string"&&typeof t.lastModified=="number"&&ka(t),ka=t=>t!=null&&typeof t=="object"&&typeof t.size=="number"&&typeof t.type=="string"&&typeof t.text=="function"&&typeof t.slice=="function"&&typeof t.arrayBuffer=="function",V0=t=>Vp(t)||Bp(t)||Lp(t);async function Hp(t,e,n){var s;if(t=await t,Vp(t))return t;if(Bp(t)){const i=await t.blob();e||(e=new URL(t.url).pathname.split(/[\\/]/).pop()??"unknown_file");const a=ka(i)?[await i.arrayBuffer()]:[i];return new gl(a,e,n)}const r=await H0(t);if(e||(e=K0(t)??"unknown_file"),!(n!=null&&n.type)){const i=(s=r[0])==null?void 0:s.type;typeof i=="string"&&(n={...n,type:i})}return new gl(r,e,n)}async function H0(t){var n;let e=[];if(typeof t=="string"||ArrayBuffer.isView(t)||t instanceof ArrayBuffer)e.push(t);else if(ka(t))e.push(await t.arrayBuffer());else if(Q0(t))for await(const r of t)e.push(r);else throw new Error(`Unexpected data type: ${typeof t}; constructor: ${(n=t==null?void 0:t.constructor)==null?void 0:n.name}; props: ${W0(t)}`);return e}function W0(t){return`[${Object.getOwnPropertyNames(t).map(n=>`"${n}"`).join(", ")}]`}function K0(t){var e;return fo(t.name)||fo(t.filename)||((e=fo(t.path))==null?void 0:e.split(/[\\/]/).pop())}const fo=t=>{if(typeof t=="string")return t;if(typeof Buffer<"u"&&t instanceof Buffer)return String(t)},Q0=t=>t!=null&&typeof t=="object"&&typeof t[Symbol.asyncIterator]=="function",_f=t=>t&&typeof t=="object"&&t.body&&t[Symbol.toStringTag]==="MultipartBody",En=async t=>{const e=await J0(t.body);return Pp(e,t)},J0=async t=>{const e=new Cp;return await Promise.all(Object.entries(t||{}).map(([n,r])=>yl(e,n,r))),e},yl=async(t,e,n)=>{if(n!==void 0){if(n==null)throw new TypeError(`Received null for "${e}"; to pass null in FormData, you must use the string 'null'`);if(typeof n=="string"||typeof n=="number"||typeof n=="boolean")t.append(e,String(n));else if(V0(n)){const r=await Hp(n);t.append(e,r)}else if(Array.isArray(n))await Promise.all(n.map(r=>yl(t,e+"[]",r)));else if(typeof n=="object")await Promise.all(Object.entries(n).map(([r,s])=>yl(t,`${e}[${r}]`,s)));else throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${n} instead`)}};var rr={},X0=function(t,e,n,r,s){if(typeof e=="function"?t!==e||!s:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(t,n),n},G0=function(t,e,n,r){if(n==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?t!==e||!r:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?r:n==="a"?r.call(t):r?r.value:e.get(t)},qs;Op();async function Wp(t){var a;const{response:e}=t;if(t.options.stream)return qt("response",e.status,e.url,e.headers,e.body),t.options.__streamClass?t.options.__streamClass.fromSSEResponse(e,t.controller):yt.fromSSEResponse(e,t.controller);if(e.status===204)return null;if(t.options.__binaryResponse)return e;const n=e.headers.get("content-type"),r=(a=n==null?void 0:n.split(";")[0])==null?void 0:a.trim();if((r==null?void 0:r.includes("application/json"))||(r==null?void 0:r.endsWith("+json"))){const o=await e.json();return qt("response",e.status,e.url,e.headers,o),Kp(o,e)}const i=await e.text();return qt("response",e.status,e.url,e.headers,i),i}function Kp(t,e){return!t||typeof t!="object"||Array.isArray(t)?t:Object.defineProperty(t,"_request_id",{value:e.headers.get("x-request-id"),enumerable:!1})}class Ca extends Promise{constructor(e,n=Wp){super(r=>{r(null)}),this.responsePromise=e,this.parseResponse=n}_thenUnwrap(e){return new Ca(this.responsePromise,async n=>Kp(e(await this.parseResponse(n),n),n.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){const[e,n]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:n,request_id:n.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(this.parseResponse)),this.parsedPromise}then(e,n){return this.parse().then(e,n)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}class Y0{constructor({baseURL:e,maxRetries:n=2,timeout:r=6e5,httpAgent:s,fetch:i}){this.baseURL=e,this.maxRetries=ho("maxRetries",n),this.timeout=ho("timeout",r),this.httpAgent=s,this.fetch=i??kp}authHeaders(e){return{}}defaultHeaders(e){return{Accept:"application/json","Content-Type":"application/json","User-Agent":this.getUserAgent(),...nv(),...this.authHeaders(e)}}validateHeaders(e,n){}defaultIdempotencyKey(){return`stainless-node-retry-${av()}`}get(e,n){return this.methodRequest("get",e,n)}post(e,n){return this.methodRequest("post",e,n)}patch(e,n){return this.methodRequest("patch",e,n)}put(e,n){return this.methodRequest("put",e,n)}delete(e,n){return this.methodRequest("delete",e,n)}methodRequest(e,n,r){return this.request(Promise.resolve(r).then(async s=>{const i=s&&ka(s==null?void 0:s.body)?new DataView(await s.body.arrayBuffer()):(s==null?void 0:s.body)instanceof DataView?s.body:(s==null?void 0:s.body)instanceof ArrayBuffer?new DataView(s.body):s&&ArrayBuffer.isView(s==null?void 0:s.body)?new DataView(s.body.buffer):s==null?void 0:s.body;return{method:e,path:n,...s,body:i}}))}getAPIList(e,n,r){return this.requestAPIList(n,{method:"get",path:e,...r})}calculateContentLength(e){if(typeof e=="string"){if(typeof Buffer<"u")return Buffer.byteLength(e,"utf8").toString();if(typeof TextEncoder<"u")return new TextEncoder().encode(e).length.toString()}else if(ArrayBuffer.isView(e))return e.byteLength.toString();return null}buildRequest(e,{retryCount:n=0}={}){var w;const r={...e},{method:s,path:i,query:a,headers:o={}}=r,l=ArrayBuffer.isView(r.body)||r.__binaryRequest&&typeof r.body=="string"?r.body:_f(r.body)?r.body.body:r.body?JSON.stringify(r.body,null,2):null,u=this.calculateContentLength(l),h=this.buildURL(i,a);"timeout"in r&&ho("timeout",r.timeout),r.timeout=r.timeout??this.timeout;const d=r.httpAgent??this.httpAgent??Rp(h),c=r.timeout+1e3;typeof((w=d==null?void 0:d.options)==null?void 0:w.timeout)=="number"&&c>(d.options.timeout??0)&&(d.options.timeout=c),this.idempotencyHeader&&s!=="get"&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),o[this.idempotencyHeader]=e.idempotencyKey);const g=this.buildHeaders({options:r,headers:o,contentLength:u,retryCount:n});return{req:{method:s,...l&&{body:l},headers:g,...d&&{agent:d},signal:r.signal??null},url:h,timeout:r.timeout}}buildHeaders({options:e,headers:n,contentLength:r,retryCount:s}){const i={};r&&(i["content-length"]=r);const a=this.defaultHeaders(e);return Ef(i,a),Ef(i,n),_f(e.body)&&Gr!=="node"&&delete i["content-type"],ei(a,"x-stainless-retry-count")===void 0&&ei(n,"x-stainless-retry-count")===void 0&&(i["x-stainless-retry-count"]=String(s)),ei(a,"x-stainless-timeout")===void 0&&ei(n,"x-stainless-timeout")===void 0&&e.timeout&&(i["x-stainless-timeout"]=String(Math.trunc(e.timeout/1e3))),this.validateHeaders(i,n),i}async prepareOptions(e){}async prepareRequest(e,{url:n,options:r}){}parseHeaders(e){return e?Symbol.iterator in e?Object.fromEntries(Array.from(e).map(n=>[...n])):{...e}:{}}makeStatusError(e,n,r,s){return ye.generate(e,n,r,s)}request(e,n=null){return new Ca(this.makeRequest(e,n))}async makeRequest(e,n){var d,c;const r=await e,s=r.maxRetries??this.maxRetries;n==null&&(n=s),await this.prepareOptions(r);const{req:i,url:a,timeout:o}=this.buildRequest(r,{retryCount:s-n});if(await this.prepareRequest(i,{url:a,options:r}),qt("request",a,r,i.headers),(d=r.signal)!=null&&d.aborted)throw new Qe;const l=new AbortController,u=await this.fetchWithTimeout(a,i,o,l).catch(vl);if(u instanceof Error){if((c=r.signal)!=null&&c.aborted)throw new Qe;if(n)return this.retryRequest(r,n);throw u.name==="AbortError"?new Cu:new _a({cause:u})}const h=Jp(u.headers);if(!u.ok){if(n&&this.shouldRetry(u)){const f=`retrying, ${n} attempts remaining`;return qt(`response (error; ${f})`,u.status,a,h),this.retryRequest(r,n,h)}const g=await u.text().catch(f=>vl(f).message),v=rv(g),w=v?void 0:g;throw qt(`response (error; ${n?"(error; no more retries left)":"(error; not retryable)"})`,u.status,a,h,w),this.makeStatusError(u.status,v,w,h)}return{response:u,options:r,controller:l}}requestAPIList(e,n){const r=this.makeRequest(n,null);return new q0(this,r,e)}buildURL(e,n){const r=iv(e)?new URL(e):new URL(this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),s=this.defaultQuery();return Xp(s)||(n={...s,...n}),typeof n=="object"&&n&&!Array.isArray(n)&&(r.search=this.stringifyQuery(n)),r.toString()}stringifyQuery(e){return Object.entries(e).filter(([n,r])=>typeof r<"u").map(([n,r])=>{if(typeof r=="string"||typeof r=="number"||typeof r=="boolean")return`${encodeURIComponent(n)}=${encodeURIComponent(r)}`;if(r===null)return`${encodeURIComponent(n)}=`;throw new I(`Cannot stringify type ${typeof r}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`)}).join("&")}async fetchWithTimeout(e,n,r,s){const{signal:i,...a}=n||{};i&&i.addEventListener("abort",()=>s.abort());const o=setTimeout(()=>s.abort(),r),l={signal:s.signal,...a};return l.method&&(l.method=l.method.toUpperCase()),this.fetch.call(void 0,e,l).finally(()=>{clearTimeout(o)})}shouldRetry(e){const n=e.headers.get("x-should-retry");return n==="true"?!0:n==="false"?!1:e.status===408||e.status===409||e.status===429||e.status>=500}async retryRequest(e,n,r){let s;const i=r==null?void 0:r["retry-after-ms"];if(i){const o=parseFloat(i);Number.isNaN(o)||(s=o)}const a=r==null?void 0:r["retry-after"];if(a&&!s){const o=parseFloat(a);Number.isNaN(o)?s=Date.parse(a)-Date.now():s=o*1e3}if(!(s&&0<=s&&s<60*1e3)){const o=e.maxRetries??this.maxRetries;s=this.calculateDefaultRetryTimeoutMillis(n,o)}return await Es(s),this.makeRequest(e,n-1)}calculateDefaultRetryTimeoutMillis(e,n){const i=n-e,a=Math.min(.5*Math.pow(2,i),8),o=1-Math.random()*.25;return a*o*1e3}getUserAgent(){return`${this.constructor.name}/JS ${Fn}`}}class Qp{constructor(e,n,r,s){qs.set(this,void 0),X0(this,qs,e),this.options=s,this.response=n,this.body=r}hasNextPage(){return this.getPaginatedItems().length?this.nextPageInfo()!=null:!1}async getNextPage(){const e=this.nextPageInfo();if(!e)throw new I("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");const n={...this.options};if("params"in e&&typeof n.query=="object")n.query={...n.query,...e.params};else if("url"in e){const r=[...Object.entries(n.query||{}),...e.url.searchParams.entries()];for(const[s,i]of r)e.url.searchParams.set(s,i);n.query=void 0,n.path=e.url.toString()}return await G0(this,qs,"f").requestAPIList(this.constructor,n)}async*iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async*[(qs=new WeakMap,Symbol.asyncIterator)](){for await(const e of this.iterPages())for(const n of e.getPaginatedItems())yield n}}class q0 extends Ca{constructor(e,n,r){super(n,async s=>new r(e,s.response,await Wp(s),s.options))}async*[Symbol.asyncIterator](){const e=await this;for await(const n of e)yield n}}const Jp=t=>new Proxy(Object.fromEntries(t.entries()),{get(e,n){const r=n.toString();return e[r.toLowerCase()]||e[r]}}),Z0={method:!0,path:!0,query:!0,body:!0,headers:!0,maxRetries:!0,stream:!0,timeout:!0,httpAgent:!0,signal:!0,idempotencyKey:!0,__metadata:!0,__binaryRequest:!0,__binaryResponse:!0,__streamClass:!0},Z=t=>typeof t=="object"&&t!==null&&!Xp(t)&&Object.keys(t).every(e=>Gp(Z0,e)),ev=()=>{var e;if(typeof Deno<"u"&&Deno.build!=null)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Fn,"X-Stainless-OS":kf(Deno.build.os),"X-Stainless-Arch":Sf(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":typeof Deno.version=="string"?Deno.version:((e=Deno.version)==null?void 0:e.deno)??"unknown"};if(typeof EdgeRuntime<"u")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Fn,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":process.version};if(Object.prototype.toString.call(typeof process<"u"?process:0)==="[object process]")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Fn,"X-Stainless-OS":kf(process.platform),"X-Stainless-Arch":Sf(process.arch),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":process.version};const t=tv();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Fn,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Fn,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}};function tv(){if(typeof navigator>"u"||!navigator)return null;const t=[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}];for(const{key:e,pattern:n}of t){const r=n.exec(navigator.userAgent);if(r){const s=r[1]||0,i=r[2]||0,a=r[3]||0;return{browser:e,version:`${s}.${i}.${a}`}}}return null}const Sf=t=>t==="x32"?"x32":t==="x86_64"||t==="x64"?"x64":t==="arm"?"arm":t==="aarch64"||t==="arm64"?"arm64":t?`other:${t}`:"unknown",kf=t=>(t=t.toLowerCase(),t.includes("ios")?"iOS":t==="android"?"Android":t==="darwin"?"MacOS":t==="win32"?"Windows":t==="freebsd"?"FreeBSD":t==="openbsd"?"OpenBSD":t==="linux"?"Linux":t?`Other:${t}`:"Unknown");let Cf;const nv=()=>Cf??(Cf=ev()),rv=t=>{try{return JSON.parse(t)}catch{return}},sv=/^[a-z][a-z0-9+.-]*:/i,iv=t=>sv.test(t),Es=t=>new Promise(e=>setTimeout(e,t)),ho=(t,e)=>{if(typeof e!="number"||!Number.isInteger(e))throw new I(`${t} must be an integer`);if(e<0)throw new I(`${t} must be a positive integer`);return e},vl=t=>{if(t instanceof Error)return t;if(typeof t=="object"&&t!==null)try{return new Error(JSON.stringify(t))}catch{}return new Error(t)},Zs=t=>{var e,n,r,s;if(typeof process<"u")return((e=rr==null?void 0:rr[t])==null?void 0:e.trim())??void 0;if(typeof Deno<"u")return(s=(r=(n=Deno.env)==null?void 0:n.get)==null?void 0:r.call(n,t))==null?void 0:s.trim()};function Xp(t){if(!t)return!0;for(const e in t)return!1;return!0}function Gp(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function Ef(t,e){for(const n in e){if(!Gp(e,n))continue;const r=n.toLowerCase();if(!r)continue;const s=e[n];s===null?delete t[r]:s!==void 0&&(t[r]=s)}}const Pf=new Set(["authorization","api-key"]);function qt(t,...e){if(typeof process<"u"&&(rr==null?void 0:rr.DEBUG)==="true"){const n=e.map(r=>{if(!r)return r;if(r.headers){const i={...r,headers:{...r.headers}};for(const a in r.headers)Pf.has(a.toLowerCase())&&(i.headers[a]="REDACTED");return i}let s=null;for(const i in r)Pf.has(i.toLowerCase())&&(s??(s={...r}),s[i]="REDACTED");return s??r});console.log(`OpenAI:DEBUG:${t}`,...n)}}const av=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,t=>{const e=Math.random()*16|0;return(t==="x"?e:e&3|8).toString(16)}),ov=()=>typeof window<"u"&&typeof window.document<"u"&&typeof navigator<"u",lv=t=>typeof(t==null?void 0:t.get)=="function",ei=(t,e)=>{var r;const n=e.toLowerCase();if(lv(t)){const s=((r=e[0])==null?void 0:r.toUpperCase())+e.substring(1).replace(/([^\w])(\w)/g,(i,a,o)=>a+o.toUpperCase());for(const i of[e,n,e.toUpperCase(),s]){const a=t.get(i);if(a)return a}}for(const[s,i]of Object.entries(t))if(s.toLowerCase()===n)return Array.isArray(i)?(i.length<=1||console.warn(`Received ${i.length} entries for the ${e} header, using the first entry.`),i[0]):i},uv=t=>{if(typeof Buffer<"u"){const e=Buffer.from(t,"base64");return Array.from(new Float32Array(e.buffer,e.byteOffset,e.length/Float32Array.BYTES_PER_ELEMENT))}else{const e=atob(t),n=e.length,r=new Uint8Array(n);for(let s=0;s<n;s++)r[s]=e.charCodeAt(s);return Array.from(new Float32Array(r.buffer))}};function po(t){return t!=null&&typeof t=="object"&&!Array.isArray(t)}class Ea extends Qp{constructor(e,n,r,s){super(e,n,r,s),this.data=r.data||[],this.object=r.object}getPaginatedItems(){return this.data??[]}nextPageParams(){return null}nextPageInfo(){return null}}class oe extends Qp{constructor(e,n,r,s){super(e,n,r,s),this.data=r.data||[],this.has_more=r.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return this.has_more===!1?!1:super.hasNextPage()}nextPageParams(){const e=this.nextPageInfo();if(!e)return null;if("params"in e)return e.params;const n=Object.fromEntries(e.url.searchParams);return Object.keys(n).length?n:null}nextPageInfo(){var r;const e=this.getPaginatedItems();if(!e.length)return null;const n=(r=e[e.length-1])==null?void 0:r.id;return n?{params:{after:n}}:null}}class b{constructor(e){this._client=e}}let Yp=class extends b{list(e,n={},r){return Z(n)?this.list(e,{},n):this._client.getAPIList(`/chat/completions/${e}/messages`,cv,{query:n,...r})}},Pa=class extends b{constructor(){super(...arguments),this.messages=new Yp(this._client)}create(e,n){return this._client.post("/chat/completions",{body:e,...n,stream:e.stream??!1})}retrieve(e,n){return this._client.get(`/chat/completions/${e}`,n)}update(e,n,r){return this._client.post(`/chat/completions/${e}`,{body:n,...r})}list(e={},n){return Z(e)?this.list({},e):this._client.getAPIList("/chat/completions",Ra,{query:e,...n})}del(e,n){return this._client.delete(`/chat/completions/${e}`,n)}};class Ra extends oe{}class cv extends oe{}Pa.ChatCompletionsPage=Ra;Pa.Messages=Yp;let Na=class extends b{constructor(){super(...arguments),this.completions=new Pa(this._client)}};Na.Completions=Pa;Na.ChatCompletionsPage=Ra;class qp extends b{create(e,n){return this._client.post("/audio/speech",{body:e,...n,headers:{Accept:"application/octet-stream",...n==null?void 0:n.headers},__binaryResponse:!0})}}class Zp extends b{create(e,n){return this._client.post("/audio/transcriptions",En({body:e,...n,stream:e.stream??!1,__metadata:{model:e.model}}))}}class em extends b{create(e,n){return this._client.post("/audio/translations",En({body:e,...n,__metadata:{model:e.model}}))}}class Ps extends b{constructor(){super(...arguments),this.transcriptions=new Zp(this._client),this.translations=new em(this._client),this.speech=new qp(this._client)}}Ps.Transcriptions=Zp;Ps.Translations=em;Ps.Speech=qp;class Eu extends b{create(e,n){return this._client.post("/batches",{body:e,...n})}retrieve(e,n){return this._client.get(`/batches/${e}`,n)}list(e={},n){return Z(e)?this.list({},e):this._client.getAPIList("/batches",Pu,{query:e,...n})}cancel(e,n){return this._client.post(`/batches/${e}/cancel`,n)}}class Pu extends oe{}Eu.BatchesPage=Pu;var Ze=function(t,e,n,r,s){if(r==="m")throw new TypeError("Private method is not writable");if(r==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?t!==e||!s:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return r==="a"?s.call(t,n):s?s.value=n:e.set(t,n),n},X=function(t,e,n,r){if(n==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?t!==e||!r:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?r:n==="a"?r.call(t):r?r.value:e.get(t)},wl,_i,Si,$r,Fr,ki,Mr,St,jr,Yi,qi,Mn,tm;class Ru{constructor(){wl.add(this),this.controller=new AbortController,_i.set(this,void 0),Si.set(this,()=>{}),$r.set(this,()=>{}),Fr.set(this,void 0),ki.set(this,()=>{}),Mr.set(this,()=>{}),St.set(this,{}),jr.set(this,!1),Yi.set(this,!1),qi.set(this,!1),Mn.set(this,!1),Ze(this,_i,new Promise((e,n)=>{Ze(this,Si,e,"f"),Ze(this,$r,n,"f")}),"f"),Ze(this,Fr,new Promise((e,n)=>{Ze(this,ki,e,"f"),Ze(this,Mr,n,"f")}),"f"),X(this,_i,"f").catch(()=>{}),X(this,Fr,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},X(this,wl,"m",tm).bind(this))},0)}_connected(){this.ended||(X(this,Si,"f").call(this),this._emit("connect"))}get ended(){return X(this,jr,"f")}get errored(){return X(this,Yi,"f")}get aborted(){return X(this,qi,"f")}abort(){this.controller.abort()}on(e,n){return(X(this,St,"f")[e]||(X(this,St,"f")[e]=[])).push({listener:n}),this}off(e,n){const r=X(this,St,"f")[e];if(!r)return this;const s=r.findIndex(i=>i.listener===n);return s>=0&&r.splice(s,1),this}once(e,n){return(X(this,St,"f")[e]||(X(this,St,"f")[e]=[])).push({listener:n,once:!0}),this}emitted(e){return new Promise((n,r)=>{Ze(this,Mn,!0,"f"),e!=="error"&&this.once("error",r),this.once(e,n)})}async done(){Ze(this,Mn,!0,"f"),await X(this,Fr,"f")}_emit(e,...n){if(X(this,jr,"f"))return;e==="end"&&(Ze(this,jr,!0,"f"),X(this,ki,"f").call(this));const r=X(this,St,"f")[e];if(r&&(X(this,St,"f")[e]=r.filter(s=>!s.once),r.forEach(({listener:s})=>s(...n))),e==="abort"){const s=n[0];!X(this,Mn,"f")&&!(r!=null&&r.length)&&Promise.reject(s),X(this,$r,"f").call(this,s),X(this,Mr,"f").call(this,s),this._emit("end");return}if(e==="error"){const s=n[0];!X(this,Mn,"f")&&!(r!=null&&r.length)&&Promise.reject(s),X(this,$r,"f").call(this,s),X(this,Mr,"f").call(this,s),this._emit("end")}}_emitFinal(){}}_i=new WeakMap,Si=new WeakMap,$r=new WeakMap,Fr=new WeakMap,ki=new WeakMap,Mr=new WeakMap,St=new WeakMap,jr=new WeakMap,Yi=new WeakMap,qi=new WeakMap,Mn=new WeakMap,wl=new WeakSet,tm=function(e){if(Ze(this,Yi,!0,"f"),e instanceof Error&&e.name==="AbortError"&&(e=new Qe),e instanceof Qe)return Ze(this,qi,!0,"f"),this._emit("abort",e);if(e instanceof I)return this._emit("error",e);if(e instanceof Error){const n=new I(e.message);return n.cause=e,this._emit("error",n)}return this._emit("error",new I(String(e)))};var O=function(t,e,n,r){if(n==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?t!==e||!r:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?r:n==="a"?r.call(t):r?r.value:e.get(t)},$e=function(t,e,n,r,s){if(r==="m")throw new TypeError("Private method is not writable");if(r==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?t!==e||!s:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return r==="a"?s.call(t,n):s?s.value=n:e.set(t,n),n},pe,xl,mt,Ci,tt,gn,Gn,dn,Zi,Me,Ei,Pi,Yr,Dr,zr,Rf,Nf,Lf,Of,Af,If,bf;class st extends Ru{constructor(){super(...arguments),pe.add(this),xl.set(this,[]),mt.set(this,{}),Ci.set(this,{}),tt.set(this,void 0),gn.set(this,void 0),Gn.set(this,void 0),dn.set(this,void 0),Zi.set(this,void 0),Me.set(this,void 0),Ei.set(this,void 0),Pi.set(this,void 0),Yr.set(this,void 0)}[(xl=new WeakMap,mt=new WeakMap,Ci=new WeakMap,tt=new WeakMap,gn=new WeakMap,Gn=new WeakMap,dn=new WeakMap,Zi=new WeakMap,Me=new WeakMap,Ei=new WeakMap,Pi=new WeakMap,Yr=new WeakMap,pe=new WeakSet,Symbol.asyncIterator)](){const e=[],n=[];let r=!1;return this.on("event",s=>{const i=n.shift();i?i.resolve(s):e.push(s)}),this.on("end",()=>{r=!0;for(const s of n)s.resolve(void 0);n.length=0}),this.on("abort",s=>{r=!0;for(const i of n)i.reject(s);n.length=0}),this.on("error",s=>{r=!0;for(const i of n)i.reject(s);n.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((i,a)=>n.push({resolve:i,reject:a})).then(i=>i?{value:i,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){const n=new st;return n._run(()=>n._fromReadableStream(e)),n}async _fromReadableStream(e,n){var i;const r=n==null?void 0:n.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),this._connected();const s=yt.fromReadableStream(e,this.controller);for await(const a of s)O(this,pe,"m",Dr).call(this,a);if((i=s.controller.signal)!=null&&i.aborted)throw new Qe;return this._addRun(O(this,pe,"m",zr).call(this))}toReadableStream(){return new yt(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,n,r,s,i){const a=new st;return a._run(()=>a._runToolAssistantStream(e,n,r,s,{...i,headers:{...i==null?void 0:i.headers,"X-Stainless-Helper-Method":"stream"}})),a}async _createToolAssistantStream(e,n,r,s,i){var u;const a=i==null?void 0:i.signal;a&&(a.aborted&&this.controller.abort(),a.addEventListener("abort",()=>this.controller.abort()));const o={...s,stream:!0},l=await e.submitToolOutputs(n,r,o,{...i,signal:this.controller.signal});this._connected();for await(const h of l)O(this,pe,"m",Dr).call(this,h);if((u=l.controller.signal)!=null&&u.aborted)throw new Qe;return this._addRun(O(this,pe,"m",zr).call(this))}static createThreadAssistantStream(e,n,r){const s=new st;return s._run(()=>s._threadAssistantStream(e,n,{...r,headers:{...r==null?void 0:r.headers,"X-Stainless-Helper-Method":"stream"}})),s}static createAssistantStream(e,n,r,s){const i=new st;return i._run(()=>i._runAssistantStream(e,n,r,{...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}})),i}currentEvent(){return O(this,Ei,"f")}currentRun(){return O(this,Pi,"f")}currentMessageSnapshot(){return O(this,tt,"f")}currentRunStepSnapshot(){return O(this,Yr,"f")}async finalRunSteps(){return await this.done(),Object.values(O(this,mt,"f"))}async finalMessages(){return await this.done(),Object.values(O(this,Ci,"f"))}async finalRun(){if(await this.done(),!O(this,gn,"f"))throw Error("Final run was not received.");return O(this,gn,"f")}async _createThreadAssistantStream(e,n,r){var o;const s=r==null?void 0:r.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort()));const i={...n,stream:!0},a=await e.createAndRun(i,{...r,signal:this.controller.signal});this._connected();for await(const l of a)O(this,pe,"m",Dr).call(this,l);if((o=a.controller.signal)!=null&&o.aborted)throw new Qe;return this._addRun(O(this,pe,"m",zr).call(this))}async _createAssistantStream(e,n,r,s){var l;const i=s==null?void 0:s.signal;i&&(i.aborted&&this.controller.abort(),i.addEventListener("abort",()=>this.controller.abort()));const a={...r,stream:!0},o=await e.create(n,a,{...s,signal:this.controller.signal});this._connected();for await(const u of o)O(this,pe,"m",Dr).call(this,u);if((l=o.controller.signal)!=null&&l.aborted)throw new Qe;return this._addRun(O(this,pe,"m",zr).call(this))}static accumulateDelta(e,n){for(const[r,s]of Object.entries(n)){if(!e.hasOwnProperty(r)){e[r]=s;continue}let i=e[r];if(i==null){e[r]=s;continue}if(r==="index"||r==="type"){e[r]=s;continue}if(typeof i=="string"&&typeof s=="string")i+=s;else if(typeof i=="number"&&typeof s=="number")i+=s;else if(po(i)&&po(s))i=this.accumulateDelta(i,s);else if(Array.isArray(i)&&Array.isArray(s)){if(i.every(a=>typeof a=="string"||typeof a=="number")){i.push(...s);continue}for(const a of s){if(!po(a))throw new Error(`Expected array delta entry to be an object but got: ${a}`);const o=a.index;if(o==null)throw console.error(a),new Error("Expected array delta entry to have an `index` property");if(typeof o!="number")throw new Error(`Expected array delta entry \`index\` property to be a number but got ${o}`);const l=i[o];l==null?i.push(a):i[o]=this.accumulateDelta(l,a)}continue}else throw Error(`Unhandled record type: ${r}, deltaValue: ${s}, accValue: ${i}`);e[r]=i}return e}_addRun(e){return e}async _threadAssistantStream(e,n,r){return await this._createThreadAssistantStream(n,e,r)}async _runAssistantStream(e,n,r,s){return await this._createAssistantStream(n,e,r,s)}async _runToolAssistantStream(e,n,r,s,i){return await this._createToolAssistantStream(r,e,n,s,i)}}Dr=function(e){if(!this.ended)switch($e(this,Ei,e,"f"),O(this,pe,"m",Lf).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":O(this,pe,"m",bf).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":O(this,pe,"m",Nf).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":O(this,pe,"m",Rf).call(this,e);break;case"error":throw new Error("Encountered an error event in event processing - errors should be processed earlier")}},zr=function(){if(this.ended)throw new I("stream has ended, this shouldn't happen");if(!O(this,gn,"f"))throw Error("Final run has not been received");return O(this,gn,"f")},Rf=function(e){const[n,r]=O(this,pe,"m",Af).call(this,e,O(this,tt,"f"));$e(this,tt,n,"f"),O(this,Ci,"f")[n.id]=n;for(const s of r){const i=n.content[s.index];(i==null?void 0:i.type)=="text"&&this._emit("textCreated",i.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,n),e.data.delta.content)for(const s of e.data.delta.content){if(s.type=="text"&&s.text){let i=s.text,a=n.content[s.index];if(a&&a.type=="text")this._emit("textDelta",i,a.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(s.index!=O(this,Gn,"f")){if(O(this,dn,"f"))switch(O(this,dn,"f").type){case"text":this._emit("textDone",O(this,dn,"f").text,O(this,tt,"f"));break;case"image_file":this._emit("imageFileDone",O(this,dn,"f").image_file,O(this,tt,"f"));break}$e(this,Gn,s.index,"f")}$e(this,dn,n.content[s.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(O(this,Gn,"f")!==void 0){const s=e.data.content[O(this,Gn,"f")];if(s)switch(s.type){case"image_file":this._emit("imageFileDone",s.image_file,O(this,tt,"f"));break;case"text":this._emit("textDone",s.text,O(this,tt,"f"));break}}O(this,tt,"f")&&this._emit("messageDone",e.data),$e(this,tt,void 0,"f")}},Nf=function(e){const n=O(this,pe,"m",Of).call(this,e);switch($e(this,Yr,n,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":const r=e.data.delta;if(r.step_details&&r.step_details.type=="tool_calls"&&r.step_details.tool_calls&&n.step_details.type=="tool_calls")for(const i of r.step_details.tool_calls)i.index==O(this,Zi,"f")?this._emit("toolCallDelta",i,n.step_details.tool_calls[i.index]):(O(this,Me,"f")&&this._emit("toolCallDone",O(this,Me,"f")),$e(this,Zi,i.index,"f"),$e(this,Me,n.step_details.tool_calls[i.index],"f"),O(this,Me,"f")&&this._emit("toolCallCreated",O(this,Me,"f")));this._emit("runStepDelta",e.data.delta,n);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":$e(this,Yr,void 0,"f"),e.data.step_details.type=="tool_calls"&&O(this,Me,"f")&&(this._emit("toolCallDone",O(this,Me,"f")),$e(this,Me,void 0,"f")),this._emit("runStepDone",e.data,n);break}},Lf=function(e){O(this,xl,"f").push(e),this._emit("event",e)},Of=function(e){switch(e.event){case"thread.run.step.created":return O(this,mt,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let n=O(this,mt,"f")[e.data.id];if(!n)throw Error("Received a RunStepDelta before creation of a snapshot");let r=e.data;if(r.delta){const s=st.accumulateDelta(n,r.delta);O(this,mt,"f")[e.data.id]=s}return O(this,mt,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":O(this,mt,"f")[e.data.id]=e.data;break}if(O(this,mt,"f")[e.data.id])return O(this,mt,"f")[e.data.id];throw new Error("No snapshot available")},Af=function(e,n){let r=[];switch(e.event){case"thread.message.created":return[e.data,r];case"thread.message.delta":if(!n)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let s=e.data;if(s.delta.content)for(const i of s.delta.content)if(i.index in n.content){let a=n.content[i.index];n.content[i.index]=O(this,pe,"m",If).call(this,i,a)}else n.content[i.index]=i,r.push(i);return[n,r];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(n)return[n,r];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},If=function(e,n){return st.accumulateDelta(n,e)},bf=function(e){switch($e(this,Pi,e.data,"f"),e.event){case"thread.run.created":break;case"thread.run.queued":break;case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":$e(this,gn,e.data,"f"),O(this,Me,"f")&&(this._emit("toolCallDone",O(this,Me,"f")),$e(this,Me,void 0,"f"));break}};class Nu extends b{create(e,n){return this._client.post("/assistants",{body:e,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}retrieve(e,n){return this._client.get(`/assistants/${e}`,{...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}update(e,n,r){return this._client.post(`/assistants/${e}`,{body:n,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}list(e={},n){return Z(e)?this.list({},e):this._client.getAPIList("/assistants",Lu,{query:e,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}del(e,n){return this._client.delete(`/assistants/${e}`,{...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}}class Lu extends oe{}Nu.AssistantsPage=Lu;function Tf(t){return typeof t.parse=="function"}const sr=t=>(t==null?void 0:t.role)==="assistant",nm=t=>(t==null?void 0:t.role)==="function",rm=t=>(t==null?void 0:t.role)==="tool";function Ou(t){return(t==null?void 0:t.$brand)==="auto-parseable-response-format"}function Rs(t){return(t==null?void 0:t.$brand)==="auto-parseable-tool"}function fv(t,e){return!e||!sm(e)?{...t,choices:t.choices.map(n=>({...n,message:{...n.message,parsed:null,...n.message.tool_calls?{tool_calls:n.message.tool_calls}:void 0}}))}:Au(t,e)}function Au(t,e){const n=t.choices.map(r=>{var s;if(r.finish_reason==="length")throw new Dp;if(r.finish_reason==="content_filter")throw new zp;return{...r,message:{...r.message,...r.message.tool_calls?{tool_calls:((s=r.message.tool_calls)==null?void 0:s.map(i=>hv(e,i)))??void 0}:void 0,parsed:r.message.content&&!r.message.refusal?dv(e,r.message.content):null}}});return{...t,choices:n}}function dv(t,e){var n,r;return((n=t.response_format)==null?void 0:n.type)!=="json_schema"?null:((r=t.response_format)==null?void 0:r.type)==="json_schema"?"$parseRaw"in t.response_format?t.response_format.$parseRaw(e):JSON.parse(e):null}function hv(t,e){var r;const n=(r=t.tools)==null?void 0:r.find(s=>{var i;return((i=s.function)==null?void 0:i.name)===e.function.name});return{...e,function:{...e.function,parsed_arguments:Rs(n)?n.$parseRaw(e.function.arguments):n!=null&&n.function.strict?JSON.parse(e.function.arguments):null}}}function pv(t,e){var r;if(!t)return!1;const n=(r=t.tools)==null?void 0:r.find(s=>{var i;return((i=s.function)==null?void 0:i.name)===e.function.name});return Rs(n)||(n==null?void 0:n.function.strict)||!1}function sm(t){var e;return Ou(t.response_format)?!0:((e=t.tools)==null?void 0:e.some(n=>Rs(n)||n.type==="function"&&n.function.strict===!0))??!1}function mv(t){for(const e of t??[]){if(e.type!=="function")throw new I(`Currently only \`function\` tool types support auto-parsing; Received \`${e.type}\``);if(e.function.strict!==!0)throw new I(`The \`${e.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}}var Ne=function(t,e,n,r){if(n==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?t!==e||!r:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?r:n==="a"?r.call(t):r?r.value:e.get(t)},xe,_l,ea,Sl,kl,Cl,im,El;const $f=10;class am extends Ru{constructor(){super(...arguments),xe.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){var r;this._chatCompletions.push(e),this._emit("chatCompletion",e);const n=(r=e.choices[0])==null?void 0:r.message;return n&&this._addMessage(n),e}_addMessage(e,n=!0){if("content"in e||(e.content=null),this.messages.push(e),n){if(this._emit("message",e),(nm(e)||rm(e))&&e.content)this._emit("functionCallResult",e.content);else if(sr(e)&&e.function_call)this._emit("functionCall",e.function_call);else if(sr(e)&&e.tool_calls)for(const r of e.tool_calls)r.type==="function"&&this._emit("functionCall",r.function)}}async finalChatCompletion(){await this.done();const e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new I("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),Ne(this,xe,"m",_l).call(this)}async finalMessage(){return await this.done(),Ne(this,xe,"m",ea).call(this)}async finalFunctionCall(){return await this.done(),Ne(this,xe,"m",Sl).call(this)}async finalFunctionCallResult(){return await this.done(),Ne(this,xe,"m",kl).call(this)}async totalUsage(){return await this.done(),Ne(this,xe,"m",Cl).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){const e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);const n=Ne(this,xe,"m",ea).call(this);n&&this._emit("finalMessage",n);const r=Ne(this,xe,"m",_l).call(this);r&&this._emit("finalContent",r);const s=Ne(this,xe,"m",Sl).call(this);s&&this._emit("finalFunctionCall",s);const i=Ne(this,xe,"m",kl).call(this);i!=null&&this._emit("finalFunctionCallResult",i),this._chatCompletions.some(a=>a.usage)&&this._emit("totalUsage",Ne(this,xe,"m",Cl).call(this))}async _createChatCompletion(e,n,r){const s=r==null?void 0:r.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),Ne(this,xe,"m",im).call(this,n);const i=await e.chat.completions.create({...n,stream:!1},{...r,signal:this.controller.signal});return this._connected(),this._addChatCompletion(Au(i,n))}async _runChatCompletion(e,n,r){for(const s of n.messages)this._addMessage(s,!1);return await this._createChatCompletion(e,n,r)}async _runFunctions(e,n,r){var c;const s="function",{function_call:i="auto",stream:a,...o}=n,l=typeof i!="string"&&(i==null?void 0:i.name),{maxChatCompletions:u=$f}=r||{},h={};for(const g of n.functions)h[g.name||g.function.name]=g;const d=n.functions.map(g=>({name:g.name||g.function.name,parameters:g.parameters,description:g.description}));for(const g of n.messages)this._addMessage(g,!1);for(let g=0;g<u;++g){const w=(c=(await this._createChatCompletion(e,{...o,function_call:i,functions:d,messages:[...this.messages]},r)).choices[0])==null?void 0:c.message;if(!w)throw new I("missing message in ChatCompletion response");if(!w.function_call)return;const{name:_,arguments:m}=w.function_call,f=h[_];if(f){if(l&&l!==_){const S=`Invalid function_call: ${JSON.stringify(_)}. ${JSON.stringify(l)} requested. Please try again`;this._addMessage({role:s,name:_,content:S});continue}}else{const S=`Invalid function_call: ${JSON.stringify(_)}. Available options are: ${d.map(x=>JSON.stringify(x.name)).join(", ")}. Please try again`;this._addMessage({role:s,name:_,content:S});continue}let p;try{p=Tf(f)?await f.parse(m):m}catch(S){this._addMessage({role:s,name:_,content:S instanceof Error?S.message:String(S)});continue}const y=await f.function(p,this),k=Ne(this,xe,"m",El).call(this,y);if(this._addMessage({role:s,name:_,content:k}),l)return}}async _runTools(e,n,r){var g,v,w;const s="tool",{tool_choice:i="auto",stream:a,...o}=n,l=typeof i!="string"&&((g=i==null?void 0:i.function)==null?void 0:g.name),{maxChatCompletions:u=$f}=r||{},h=n.tools.map(_=>{if(Rs(_)){if(!_.$callback)throw new I("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:_.$callback,name:_.function.name,description:_.function.description||"",parameters:_.function.parameters,parse:_.$parseRaw,strict:!0}}}return _}),d={};for(const _ of h)_.type==="function"&&(d[_.function.name||_.function.function.name]=_.function);const c="tools"in n?h.map(_=>_.type==="function"?{type:"function",function:{name:_.function.name||_.function.function.name,parameters:_.function.parameters,description:_.function.description,strict:_.function.strict}}:_):void 0;for(const _ of n.messages)this._addMessage(_,!1);for(let _=0;_<u;++_){const f=(v=(await this._createChatCompletion(e,{...o,tool_choice:i,tools:c,messages:[...this.messages]},r)).choices[0])==null?void 0:v.message;if(!f)throw new I("missing message in ChatCompletion response");if(!((w=f.tool_calls)!=null&&w.length))return;for(const p of f.tool_calls){if(p.type!=="function")continue;const y=p.id,{name:k,arguments:S}=p.function,x=d[k];if(x){if(l&&l!==k){const T=`Invalid tool_call: ${JSON.stringify(k)}. ${JSON.stringify(l)} requested. Please try again`;this._addMessage({role:s,tool_call_id:y,content:T});continue}}else{const T=`Invalid tool_call: ${JSON.stringify(k)}. Available options are: ${Object.keys(d).map(Te=>JSON.stringify(Te)).join(", ")}. Please try again`;this._addMessage({role:s,tool_call_id:y,content:T});continue}let R;try{R=Tf(x)?await x.parse(S):S}catch(T){const Te=T instanceof Error?T.message:String(T);this._addMessage({role:s,tool_call_id:y,content:Te});continue}const F=await x.function(R,this),L=Ne(this,xe,"m",El).call(this,F);if(this._addMessage({role:s,tool_call_id:y,content:L}),l)return}}}}xe=new WeakSet,_l=function(){return Ne(this,xe,"m",ea).call(this).content??null},ea=function(){let e=this.messages.length;for(;e-- >0;){const n=this.messages[e];if(sr(n)){const{function_call:r,...s}=n,i={...s,content:n.content??null,refusal:n.refusal??null};return r&&(i.function_call=r),i}}throw new I("stream ended without producing a ChatCompletionMessage with role=assistant")},Sl=function(){var e,n;for(let r=this.messages.length-1;r>=0;r--){const s=this.messages[r];if(sr(s)&&(s!=null&&s.function_call))return s.function_call;if(sr(s)&&((e=s==null?void 0:s.tool_calls)!=null&&e.length))return(n=s.tool_calls.at(-1))==null?void 0:n.function}},kl=function(){for(let e=this.messages.length-1;e>=0;e--){const n=this.messages[e];if(nm(n)&&n.content!=null||rm(n)&&n.content!=null&&typeof n.content=="string"&&this.messages.some(r=>{var s;return r.role==="assistant"&&((s=r.tool_calls)==null?void 0:s.some(i=>i.type==="function"&&i.id===n.tool_call_id))}))return n.content}},Cl=function(){const e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(const{usage:n}of this._chatCompletions)n&&(e.completion_tokens+=n.completion_tokens,e.prompt_tokens+=n.prompt_tokens,e.total_tokens+=n.total_tokens);return e},im=function(e){if(e.n!=null&&e.n>1)throw new I("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},El=function(e){return typeof e=="string"?e:e===void 0?"undefined":JSON.stringify(e)};class gs extends am{static runFunctions(e,n,r){const s=new gs,i={...r,headers:{...r==null?void 0:r.headers,"X-Stainless-Helper-Method":"runFunctions"}};return s._run(()=>s._runFunctions(e,n,i)),s}static runTools(e,n,r){const s=new gs,i={...r,headers:{...r==null?void 0:r.headers,"X-Stainless-Helper-Method":"runTools"}};return s._run(()=>s._runTools(e,n,i)),s}_addMessage(e,n=!0){super._addMessage(e,n),sr(e)&&e.content&&this._emit("content",e.content)}}const om=1,lm=2,um=4,cm=8,fm=16,dm=32,hm=64,pm=128,mm=256,gm=pm|mm,ym=fm|dm|gm|hm,vm=om|lm|ym,wm=um|cm,gv=vm|wm,ue={STR:om,NUM:lm,ARR:um,OBJ:cm,NULL:fm,BOOL:dm,NAN:hm,INFINITY:pm,MINUS_INFINITY:mm,INF:gm,SPECIAL:ym,ATOM:vm,COLLECTION:wm,ALL:gv};class yv extends Error{}class vv extends Error{}function wv(t,e=ue.ALL){if(typeof t!="string")throw new TypeError(`expecting str, got ${typeof t}`);if(!t.trim())throw new Error(`${t} is empty`);return xv(t.trim(),e)}const xv=(t,e)=>{const n=t.length;let r=0;const s=c=>{throw new yv(`${c} at position ${r}`)},i=c=>{throw new vv(`${c} at position ${r}`)},a=()=>(d(),r>=n&&s("Unexpected end of input"),t[r]==='"'?o():t[r]==="{"?l():t[r]==="["?u():t.substring(r,r+4)==="null"||ue.NULL&e&&n-r<4&&"null".startsWith(t.substring(r))?(r+=4,null):t.substring(r,r+4)==="true"||ue.BOOL&e&&n-r<4&&"true".startsWith(t.substring(r))?(r+=4,!0):t.substring(r,r+5)==="false"||ue.BOOL&e&&n-r<5&&"false".startsWith(t.substring(r))?(r+=5,!1):t.substring(r,r+8)==="Infinity"||ue.INFINITY&e&&n-r<8&&"Infinity".startsWith(t.substring(r))?(r+=8,1/0):t.substring(r,r+9)==="-Infinity"||ue.MINUS_INFINITY&e&&1<n-r&&n-r<9&&"-Infinity".startsWith(t.substring(r))?(r+=9,-1/0):t.substring(r,r+3)==="NaN"||ue.NAN&e&&n-r<3&&"NaN".startsWith(t.substring(r))?(r+=3,NaN):h()),o=()=>{const c=r;let g=!1;for(r++;r<n&&(t[r]!=='"'||g&&t[r-1]==="\\");)g=t[r]==="\\"?!g:!1,r++;if(t.charAt(r)=='"')try{return JSON.parse(t.substring(c,++r-Number(g)))}catch(v){i(String(v))}else if(ue.STR&e)try{return JSON.parse(t.substring(c,r-Number(g))+'"')}catch{return JSON.parse(t.substring(c,t.lastIndexOf("\\"))+'"')}s("Unterminated string literal")},l=()=>{r++,d();const c={};try{for(;t[r]!=="}";){if(d(),r>=n&&ue.OBJ&e)return c;const g=o();d(),r++;try{const v=a();Object.defineProperty(c,g,{value:v,writable:!0,enumerable:!0,configurable:!0})}catch(v){if(ue.OBJ&e)return c;throw v}d(),t[r]===","&&r++}}catch{if(ue.OBJ&e)return c;s("Expected '}' at end of object")}return r++,c},u=()=>{r++;const c=[];try{for(;t[r]!=="]";)c.push(a()),d(),t[r]===","&&r++}catch{if(ue.ARR&e)return c;s("Expected ']' at end of array")}return r++,c},h=()=>{if(r===0){t==="-"&&ue.NUM&e&&s("Not sure what '-' is");try{return JSON.parse(t)}catch(g){if(ue.NUM&e)try{return t[t.length-1]==="."?JSON.parse(t.substring(0,t.lastIndexOf("."))):JSON.parse(t.substring(0,t.lastIndexOf("e")))}catch{}i(String(g))}}const c=r;for(t[r]==="-"&&r++;t[r]&&!",]}".includes(t[r]);)r++;r==n&&!(ue.NUM&e)&&s("Unterminated number literal");try{return JSON.parse(t.substring(c,r))}catch{t.substring(c,r)==="-"&&ue.NUM&e&&s("Not sure what '-' is");try{return JSON.parse(t.substring(c,t.lastIndexOf("e")))}catch(v){i(String(v))}}},d=()=>{for(;r<n&&` 
\r	`.includes(t[r]);)r++};return a()},Ff=t=>wv(t,ue.ALL^ue.NUM);var On=function(t,e,n,r,s){if(r==="m")throw new TypeError("Private method is not writable");if(r==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?t!==e||!s:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return r==="a"?s.call(t,n):s?s.value=n:e.set(t,n),n},B=function(t,e,n,r){if(n==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?t!==e||!r:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?r:n==="a"?r.call(t):r?r.value:e.get(t)},re,_t,An,$t,mo,ti,go,yo,vo,ni,wo,Mf;class ys extends am{constructor(e){super(),re.add(this),_t.set(this,void 0),An.set(this,void 0),$t.set(this,void 0),On(this,_t,e,"f"),On(this,An,[],"f")}get currentChatCompletionSnapshot(){return B(this,$t,"f")}static fromReadableStream(e){const n=new ys(null);return n._run(()=>n._fromReadableStream(e)),n}static createChatCompletion(e,n,r){const s=new ys(n);return s._run(()=>s._runChatCompletion(e,{...n,stream:!0},{...r,headers:{...r==null?void 0:r.headers,"X-Stainless-Helper-Method":"stream"}})),s}async _createChatCompletion(e,n,r){var a;super._createChatCompletion;const s=r==null?void 0:r.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),B(this,re,"m",mo).call(this);const i=await e.chat.completions.create({...n,stream:!0},{...r,signal:this.controller.signal});this._connected();for await(const o of i)B(this,re,"m",go).call(this,o);if((a=i.controller.signal)!=null&&a.aborted)throw new Qe;return this._addChatCompletion(B(this,re,"m",ni).call(this))}async _fromReadableStream(e,n){var a;const r=n==null?void 0:n.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),B(this,re,"m",mo).call(this),this._connected();const s=yt.fromReadableStream(e,this.controller);let i;for await(const o of s)i&&i!==o.id&&this._addChatCompletion(B(this,re,"m",ni).call(this)),B(this,re,"m",go).call(this,o),i=o.id;if((a=s.controller.signal)!=null&&a.aborted)throw new Qe;return this._addChatCompletion(B(this,re,"m",ni).call(this))}[(_t=new WeakMap,An=new WeakMap,$t=new WeakMap,re=new WeakSet,mo=function(){this.ended||On(this,$t,void 0,"f")},ti=function(n){let r=B(this,An,"f")[n.index];return r||(r={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},B(this,An,"f")[n.index]=r,r)},go=function(n){var s,i,a,o,l,u,h,d,c,g,v,w,_,m,f;if(this.ended)return;const r=B(this,re,"m",Mf).call(this,n);this._emit("chunk",n,r);for(const p of n.choices){const y=r.choices[p.index];p.delta.content!=null&&((s=y.message)==null?void 0:s.role)==="assistant"&&((i=y.message)!=null&&i.content)&&(this._emit("content",p.delta.content,y.message.content),this._emit("content.delta",{delta:p.delta.content,snapshot:y.message.content,parsed:y.message.parsed})),p.delta.refusal!=null&&((a=y.message)==null?void 0:a.role)==="assistant"&&((o=y.message)!=null&&o.refusal)&&this._emit("refusal.delta",{delta:p.delta.refusal,snapshot:y.message.refusal}),((l=p.logprobs)==null?void 0:l.content)!=null&&((u=y.message)==null?void 0:u.role)==="assistant"&&this._emit("logprobs.content.delta",{content:(h=p.logprobs)==null?void 0:h.content,snapshot:((d=y.logprobs)==null?void 0:d.content)??[]}),((c=p.logprobs)==null?void 0:c.refusal)!=null&&((g=y.message)==null?void 0:g.role)==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:(v=p.logprobs)==null?void 0:v.refusal,snapshot:((w=y.logprobs)==null?void 0:w.refusal)??[]});const k=B(this,re,"m",ti).call(this,y);y.finish_reason&&(B(this,re,"m",vo).call(this,y),k.current_tool_call_index!=null&&B(this,re,"m",yo).call(this,y,k.current_tool_call_index));for(const S of p.delta.tool_calls??[])k.current_tool_call_index!==S.index&&(B(this,re,"m",vo).call(this,y),k.current_tool_call_index!=null&&B(this,re,"m",yo).call(this,y,k.current_tool_call_index)),k.current_tool_call_index=S.index;for(const S of p.delta.tool_calls??[]){const x=(_=y.message.tool_calls)==null?void 0:_[S.index];x!=null&&x.type&&((x==null?void 0:x.type)==="function"?this._emit("tool_calls.function.arguments.delta",{name:(m=x.function)==null?void 0:m.name,index:S.index,arguments:x.function.arguments,parsed_arguments:x.function.parsed_arguments,arguments_delta:((f=S.function)==null?void 0:f.arguments)??""}):(x==null||x.type,void 0))}}},yo=function(n,r){var a,o,l;if(B(this,re,"m",ti).call(this,n).done_tool_calls.has(r))return;const i=(a=n.message.tool_calls)==null?void 0:a[r];if(!i)throw new Error("no tool call snapshot");if(!i.type)throw new Error("tool call snapshot missing `type`");if(i.type==="function"){const u=(l=(o=B(this,_t,"f"))==null?void 0:o.tools)==null?void 0:l.find(h=>h.type==="function"&&h.function.name===i.function.name);this._emit("tool_calls.function.arguments.done",{name:i.function.name,index:r,arguments:i.function.arguments,parsed_arguments:Rs(u)?u.$parseRaw(i.function.arguments):u!=null&&u.function.strict?JSON.parse(i.function.arguments):null})}else i.type},vo=function(n){var s,i;const r=B(this,re,"m",ti).call(this,n);if(n.message.content&&!r.content_done){r.content_done=!0;const a=B(this,re,"m",wo).call(this);this._emit("content.done",{content:n.message.content,parsed:a?a.$parseRaw(n.message.content):null})}n.message.refusal&&!r.refusal_done&&(r.refusal_done=!0,this._emit("refusal.done",{refusal:n.message.refusal})),(s=n.logprobs)!=null&&s.content&&!r.logprobs_content_done&&(r.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:n.logprobs.content})),(i=n.logprobs)!=null&&i.refusal&&!r.logprobs_refusal_done&&(r.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:n.logprobs.refusal}))},ni=function(){if(this.ended)throw new I("stream has ended, this shouldn't happen");const n=B(this,$t,"f");if(!n)throw new I("request ended without sending any chunks");return On(this,$t,void 0,"f"),On(this,An,[],"f"),_v(n,B(this,_t,"f"))},wo=function(){var r;const n=(r=B(this,_t,"f"))==null?void 0:r.response_format;return Ou(n)?n:null},Mf=function(n){var r,s,i,a;let o=B(this,$t,"f");const{choices:l,...u}=n;o?Object.assign(o,u):o=On(this,$t,{...u,choices:[]},"f");for(const{delta:h,finish_reason:d,index:c,logprobs:g=null,...v}of n.choices){let w=o.choices[c];if(w||(w=o.choices[c]={finish_reason:d,index:c,message:{},logprobs:g,...v}),g)if(!w.logprobs)w.logprobs=Object.assign({},g);else{const{content:S,refusal:x,...R}=g;Object.assign(w.logprobs,R),S&&((r=w.logprobs).content??(r.content=[]),w.logprobs.content.push(...S)),x&&((s=w.logprobs).refusal??(s.refusal=[]),w.logprobs.refusal.push(...x))}if(d&&(w.finish_reason=d,B(this,_t,"f")&&sm(B(this,_t,"f")))){if(d==="length")throw new Dp;if(d==="content_filter")throw new zp}if(Object.assign(w,v),!h)continue;const{content:_,refusal:m,function_call:f,role:p,tool_calls:y,...k}=h;if(Object.assign(w.message,k),m&&(w.message.refusal=(w.message.refusal||"")+m),p&&(w.message.role=p),f&&(w.message.function_call?(f.name&&(w.message.function_call.name=f.name),f.arguments&&((i=w.message.function_call).arguments??(i.arguments=""),w.message.function_call.arguments+=f.arguments)):w.message.function_call=f),_&&(w.message.content=(w.message.content||"")+_,!w.message.refusal&&B(this,re,"m",wo).call(this)&&(w.message.parsed=Ff(w.message.content))),y){w.message.tool_calls||(w.message.tool_calls=[]);for(const{index:S,id:x,type:R,function:F,...L}of y){const T=(a=w.message.tool_calls)[S]??(a[S]={});Object.assign(T,L),x&&(T.id=x),R&&(T.type=R),F&&(T.function??(T.function={name:F.name??"",arguments:""})),F!=null&&F.name&&(T.function.name=F.name),F!=null&&F.arguments&&(T.function.arguments+=F.arguments,pv(B(this,_t,"f"),T)&&(T.function.parsed_arguments=Ff(T.function.arguments)))}}}return o},Symbol.asyncIterator)](){const e=[],n=[];let r=!1;return this.on("chunk",s=>{const i=n.shift();i?i.resolve(s):e.push(s)}),this.on("end",()=>{r=!0;for(const s of n)s.resolve(void 0);n.length=0}),this.on("abort",s=>{r=!0;for(const i of n)i.reject(s);n.length=0}),this.on("error",s=>{r=!0;for(const i of n)i.reject(s);n.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((i,a)=>n.push({resolve:i,reject:a})).then(i=>i?{value:i,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new yt(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function _v(t,e){const{id:n,choices:r,created:s,model:i,system_fingerprint:a,...o}=t,l={...o,id:n,choices:r.map(({message:u,finish_reason:h,index:d,logprobs:c,...g})=>{if(!h)throw new I(`missing finish_reason for choice ${d}`);const{content:v=null,function_call:w,tool_calls:_,...m}=u,f=u.role;if(!f)throw new I(`missing role for choice ${d}`);if(w){const{arguments:p,name:y}=w;if(p==null)throw new I(`missing function_call.arguments for choice ${d}`);if(!y)throw new I(`missing function_call.name for choice ${d}`);return{...g,message:{content:v,function_call:{arguments:p,name:y},role:f,refusal:u.refusal??null},finish_reason:h,index:d,logprobs:c}}return _?{...g,index:d,finish_reason:h,logprobs:c,message:{...m,role:f,content:v,refusal:u.refusal??null,tool_calls:_.map((p,y)=>{const{function:k,type:S,id:x,...R}=p,{arguments:F,name:L,...T}=k||{};if(x==null)throw new I(`missing choices[${d}].tool_calls[${y}].id
${ri(t)}`);if(S==null)throw new I(`missing choices[${d}].tool_calls[${y}].type
${ri(t)}`);if(L==null)throw new I(`missing choices[${d}].tool_calls[${y}].function.name
${ri(t)}`);if(F==null)throw new I(`missing choices[${d}].tool_calls[${y}].function.arguments
${ri(t)}`);return{...R,id:x,type:S,function:{...T,name:L,arguments:F}}})}}:{...g,message:{...m,content:v,role:f,refusal:u.refusal??null},finish_reason:h,index:d,logprobs:c}}),created:s,model:i,object:"chat.completion",...a?{system_fingerprint:a}:{}};return fv(l,e)}function ri(t){return JSON.stringify(t)}class ir extends ys{static fromReadableStream(e){const n=new ir(null);return n._run(()=>n._fromReadableStream(e)),n}static runFunctions(e,n,r){const s=new ir(null),i={...r,headers:{...r==null?void 0:r.headers,"X-Stainless-Helper-Method":"runFunctions"}};return s._run(()=>s._runFunctions(e,n,i)),s}static runTools(e,n,r){const s=new ir(n),i={...r,headers:{...r==null?void 0:r.headers,"X-Stainless-Helper-Method":"runTools"}};return s._run(()=>s._runTools(e,n,i)),s}}let xm=class extends b{parse(e,n){return mv(e.tools),this._client.chat.completions.create(e,{...n,headers:{...n==null?void 0:n.headers,"X-Stainless-Helper-Method":"beta.chat.completions.parse"}})._thenUnwrap(r=>Au(r,e))}runFunctions(e,n){return e.stream?ir.runFunctions(this._client,e,n):gs.runFunctions(this._client,e,n)}runTools(e,n){return e.stream?ir.runTools(this._client,e,n):gs.runTools(this._client,e,n)}stream(e,n){return ys.createChatCompletion(this._client,e,n)}};class Pl extends b{constructor(){super(...arguments),this.completions=new xm(this._client)}}(function(t){t.Completions=xm})(Pl||(Pl={}));class _m extends b{create(e,n){return this._client.post("/realtime/sessions",{body:e,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}}class Sm extends b{create(e,n){return this._client.post("/realtime/transcription_sessions",{body:e,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}}class La extends b{constructor(){super(...arguments),this.sessions=new _m(this._client),this.transcriptionSessions=new Sm(this._client)}}La.Sessions=_m;La.TranscriptionSessions=Sm;class Iu extends b{create(e,n,r){return this._client.post(`/threads/${e}/messages`,{body:n,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}retrieve(e,n,r){return this._client.get(`/threads/${e}/messages/${n}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}update(e,n,r,s){return this._client.post(`/threads/${e}/messages/${n}`,{body:r,...s,headers:{"OpenAI-Beta":"assistants=v2",...s==null?void 0:s.headers}})}list(e,n={},r){return Z(n)?this.list(e,{},n):this._client.getAPIList(`/threads/${e}/messages`,bu,{query:n,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}del(e,n,r){return this._client.delete(`/threads/${e}/messages/${n}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}}class bu extends oe{}Iu.MessagesPage=bu;class Tu extends b{retrieve(e,n,r,s={},i){return Z(s)?this.retrieve(e,n,r,{},s):this._client.get(`/threads/${e}/runs/${n}/steps/${r}`,{query:s,...i,headers:{"OpenAI-Beta":"assistants=v2",...i==null?void 0:i.headers}})}list(e,n,r={},s){return Z(r)?this.list(e,n,{},r):this._client.getAPIList(`/threads/${e}/runs/${n}/steps`,$u,{query:r,...s,headers:{"OpenAI-Beta":"assistants=v2",...s==null?void 0:s.headers}})}}class $u extends oe{}Tu.RunStepsPage=$u;let Ns=class extends b{constructor(){super(...arguments),this.steps=new Tu(this._client)}create(e,n,r){const{include:s,...i}=n;return this._client.post(`/threads/${e}/runs`,{query:{include:s},body:i,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers},stream:n.stream??!1})}retrieve(e,n,r){return this._client.get(`/threads/${e}/runs/${n}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}update(e,n,r,s){return this._client.post(`/threads/${e}/runs/${n}`,{body:r,...s,headers:{"OpenAI-Beta":"assistants=v2",...s==null?void 0:s.headers}})}list(e,n={},r){return Z(n)?this.list(e,{},n):this._client.getAPIList(`/threads/${e}/runs`,Fu,{query:n,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}cancel(e,n,r){return this._client.post(`/threads/${e}/runs/${n}/cancel`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}async createAndPoll(e,n,r){const s=await this.create(e,n,r);return await this.poll(e,s.id,r)}createAndStream(e,n,r){return st.createAssistantStream(e,this._client.beta.threads.runs,n,r)}async poll(e,n,r){const s={...r==null?void 0:r.headers,"X-Stainless-Poll-Helper":"true"};for(r!=null&&r.pollIntervalMs&&(s["X-Stainless-Custom-Poll-Interval"]=r.pollIntervalMs.toString());;){const{data:i,response:a}=await this.retrieve(e,n,{...r,headers:{...r==null?void 0:r.headers,...s}}).withResponse();switch(i.status){case"queued":case"in_progress":case"cancelling":let o=5e3;if(r!=null&&r.pollIntervalMs)o=r.pollIntervalMs;else{const l=a.headers.get("openai-poll-after-ms");if(l){const u=parseInt(l);isNaN(u)||(o=u)}}await Es(o);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return i}}}stream(e,n,r){return st.createAssistantStream(e,this._client.beta.threads.runs,n,r)}submitToolOutputs(e,n,r,s){return this._client.post(`/threads/${e}/runs/${n}/submit_tool_outputs`,{body:r,...s,headers:{"OpenAI-Beta":"assistants=v2",...s==null?void 0:s.headers},stream:r.stream??!1})}async submitToolOutputsAndPoll(e,n,r,s){const i=await this.submitToolOutputs(e,n,r,s);return await this.poll(e,i.id,s)}submitToolOutputsStream(e,n,r,s){return st.createToolAssistantStream(e,n,this._client.beta.threads.runs,r,s)}};class Fu extends oe{}Ns.RunsPage=Fu;Ns.Steps=Tu;Ns.RunStepsPage=$u;class gr extends b{constructor(){super(...arguments),this.runs=new Ns(this._client),this.messages=new Iu(this._client)}create(e={},n){return Z(e)?this.create({},e):this._client.post("/threads",{body:e,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}retrieve(e,n){return this._client.get(`/threads/${e}`,{...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}update(e,n,r){return this._client.post(`/threads/${e}`,{body:n,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}del(e,n){return this._client.delete(`/threads/${e}`,{...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}createAndRun(e,n){return this._client.post("/threads/runs",{body:e,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers},stream:e.stream??!1})}async createAndRunPoll(e,n){const r=await this.createAndRun(e,n);return await this.runs.poll(r.thread_id,r.id,n)}createAndRunStream(e,n){return st.createThreadAssistantStream(e,this._client.beta.threads,n)}}gr.Runs=Ns;gr.RunsPage=Fu;gr.Messages=Iu;gr.MessagesPage=bu;class yr extends b{constructor(){super(...arguments),this.realtime=new La(this._client),this.chat=new Pl(this._client),this.assistants=new Nu(this._client),this.threads=new gr(this._client)}}yr.Realtime=La;yr.Assistants=Nu;yr.AssistantsPage=Lu;yr.Threads=gr;class km extends b{create(e,n){return this._client.post("/completions",{body:e,...n,stream:e.stream??!1})}}class Cm extends b{retrieve(e,n,r){return this._client.get(`/containers/${e}/files/${n}/content`,{...r,headers:{Accept:"application/binary",...r==null?void 0:r.headers},__binaryResponse:!0})}}let Oa=class extends b{constructor(){super(...arguments),this.content=new Cm(this._client)}create(e,n,r){return this._client.post(`/containers/${e}/files`,En({body:n,...r}))}retrieve(e,n,r){return this._client.get(`/containers/${e}/files/${n}`,r)}list(e,n={},r){return Z(n)?this.list(e,{},n):this._client.getAPIList(`/containers/${e}/files`,Mu,{query:n,...r})}del(e,n,r){return this._client.delete(`/containers/${e}/files/${n}`,{...r,headers:{Accept:"*/*",...r==null?void 0:r.headers}})}};class Mu extends oe{}Oa.FileListResponsesPage=Mu;Oa.Content=Cm;class Ls extends b{constructor(){super(...arguments),this.files=new Oa(this._client)}create(e,n){return this._client.post("/containers",{body:e,...n})}retrieve(e,n){return this._client.get(`/containers/${e}`,n)}list(e={},n){return Z(e)?this.list({},e):this._client.getAPIList("/containers",ju,{query:e,...n})}del(e,n){return this._client.delete(`/containers/${e}`,{...n,headers:{Accept:"*/*",...n==null?void 0:n.headers}})}}class ju extends oe{}Ls.ContainerListResponsesPage=ju;Ls.Files=Oa;Ls.FileListResponsesPage=Mu;class Em extends b{create(e,n){const r=!!e.encoding_format;let s=r?e.encoding_format:"base64";r&&qt("Request","User defined encoding_format:",e.encoding_format);const i=this._client.post("/embeddings",{body:{...e,encoding_format:s},...n});return r?i:(qt("response","Decoding base64 embeddings to float32 array"),i._thenUnwrap(a=>(a&&a.data&&a.data.forEach(o=>{const l=o.embedding;o.embedding=uv(l)}),a)))}}class Du extends b{retrieve(e,n,r,s){return this._client.get(`/evals/${e}/runs/${n}/output_items/${r}`,s)}list(e,n,r={},s){return Z(r)?this.list(e,n,{},r):this._client.getAPIList(`/evals/${e}/runs/${n}/output_items`,zu,{query:r,...s})}}class zu extends oe{}Du.OutputItemListResponsesPage=zu;class Os extends b{constructor(){super(...arguments),this.outputItems=new Du(this._client)}create(e,n,r){return this._client.post(`/evals/${e}/runs`,{body:n,...r})}retrieve(e,n,r){return this._client.get(`/evals/${e}/runs/${n}`,r)}list(e,n={},r){return Z(n)?this.list(e,{},n):this._client.getAPIList(`/evals/${e}/runs`,Uu,{query:n,...r})}del(e,n,r){return this._client.delete(`/evals/${e}/runs/${n}`,r)}cancel(e,n,r){return this._client.post(`/evals/${e}/runs/${n}`,r)}}class Uu extends oe{}Os.RunListResponsesPage=Uu;Os.OutputItems=Du;Os.OutputItemListResponsesPage=zu;class As extends b{constructor(){super(...arguments),this.runs=new Os(this._client)}create(e,n){return this._client.post("/evals",{body:e,...n})}retrieve(e,n){return this._client.get(`/evals/${e}`,n)}update(e,n,r){return this._client.post(`/evals/${e}`,{body:n,...r})}list(e={},n){return Z(e)?this.list({},e):this._client.getAPIList("/evals",Bu,{query:e,...n})}del(e,n){return this._client.delete(`/evals/${e}`,n)}}class Bu extends oe{}As.EvalListResponsesPage=Bu;As.Runs=Os;As.RunListResponsesPage=Uu;let Vu=class extends b{create(e,n){return this._client.post("/files",En({body:e,...n}))}retrieve(e,n){return this._client.get(`/files/${e}`,n)}list(e={},n){return Z(e)?this.list({},e):this._client.getAPIList("/files",Hu,{query:e,...n})}del(e,n){return this._client.delete(`/files/${e}`,n)}content(e,n){return this._client.get(`/files/${e}/content`,{...n,headers:{Accept:"application/binary",...n==null?void 0:n.headers},__binaryResponse:!0})}retrieveContent(e,n){return this._client.get(`/files/${e}/content`,n)}async waitForProcessing(e,{pollInterval:n=5e3,maxWait:r=30*60*1e3}={}){const s=new Set(["processed","error","deleted"]),i=Date.now();let a=await this.retrieve(e);for(;!a.status||!s.has(a.status);)if(await Es(n),a=await this.retrieve(e),Date.now()-i>r)throw new Cu({message:`Giving up on waiting for file ${e} to finish processing after ${r} milliseconds.`});return a}};class Hu extends oe{}Vu.FileObjectsPage=Hu;class Pm extends b{}let Rm=class extends b{run(e,n){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...n})}validate(e,n){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...n})}};class Wu extends b{constructor(){super(...arguments),this.graders=new Rm(this._client)}}Wu.Graders=Rm;class Ku extends b{create(e,n,r){return this._client.getAPIList(`/fine_tuning/checkpoints/${e}/permissions`,Qu,{body:n,method:"post",...r})}retrieve(e,n={},r){return Z(n)?this.retrieve(e,{},n):this._client.get(`/fine_tuning/checkpoints/${e}/permissions`,{query:n,...r})}del(e,n,r){return this._client.delete(`/fine_tuning/checkpoints/${e}/permissions/${n}`,r)}}class Qu extends Ea{}Ku.PermissionCreateResponsesPage=Qu;let Aa=class extends b{constructor(){super(...arguments),this.permissions=new Ku(this._client)}};Aa.Permissions=Ku;Aa.PermissionCreateResponsesPage=Qu;class Ju extends b{list(e,n={},r){return Z(n)?this.list(e,{},n):this._client.getAPIList(`/fine_tuning/jobs/${e}/checkpoints`,Xu,{query:n,...r})}}class Xu extends oe{}Ju.FineTuningJobCheckpointsPage=Xu;class vr extends b{constructor(){super(...arguments),this.checkpoints=new Ju(this._client)}create(e,n){return this._client.post("/fine_tuning/jobs",{body:e,...n})}retrieve(e,n){return this._client.get(`/fine_tuning/jobs/${e}`,n)}list(e={},n){return Z(e)?this.list({},e):this._client.getAPIList("/fine_tuning/jobs",Gu,{query:e,...n})}cancel(e,n){return this._client.post(`/fine_tuning/jobs/${e}/cancel`,n)}listEvents(e,n={},r){return Z(n)?this.listEvents(e,{},n):this._client.getAPIList(`/fine_tuning/jobs/${e}/events`,Yu,{query:n,...r})}pause(e,n){return this._client.post(`/fine_tuning/jobs/${e}/pause`,n)}resume(e,n){return this._client.post(`/fine_tuning/jobs/${e}/resume`,n)}}class Gu extends oe{}class Yu extends oe{}vr.FineTuningJobsPage=Gu;vr.FineTuningJobEventsPage=Yu;vr.Checkpoints=Ju;vr.FineTuningJobCheckpointsPage=Xu;class sn extends b{constructor(){super(...arguments),this.methods=new Pm(this._client),this.jobs=new vr(this._client),this.checkpoints=new Aa(this._client),this.alpha=new Wu(this._client)}}sn.Methods=Pm;sn.Jobs=vr;sn.FineTuningJobsPage=Gu;sn.FineTuningJobEventsPage=Yu;sn.Checkpoints=Aa;sn.Alpha=Wu;class Nm extends b{}class qu extends b{constructor(){super(...arguments),this.graderModels=new Nm(this._client)}}qu.GraderModels=Nm;class Lm extends b{createVariation(e,n){return this._client.post("/images/variations",En({body:e,...n}))}edit(e,n){return this._client.post("/images/edits",En({body:e,...n}))}generate(e,n){return this._client.post("/images/generations",{body:e,...n})}}class Zu extends b{retrieve(e,n){return this._client.get(`/models/${e}`,n)}list(e){return this._client.getAPIList("/models",ec,e)}del(e,n){return this._client.delete(`/models/${e}`,n)}}class ec extends Ea{}Zu.ModelsPage=ec;class Om extends b{create(e,n){return this._client.post("/moderations",{body:e,...n})}}function Sv(t,e){return!e||!Cv(e)?{...t,output_parsed:null,output:t.output.map(n=>n.type==="function_call"?{...n,parsed_arguments:null}:n.type==="message"?{...n,content:n.content.map(r=>({...r,parsed:null}))}:n)}:Am(t,e)}function Am(t,e){const n=t.output.map(s=>{if(s.type==="function_call")return{...s,parsed_arguments:Rv(e,s)};if(s.type==="message"){const i=s.content.map(a=>a.type==="output_text"?{...a,parsed:kv(e,a.text)}:a);return{...s,content:i}}return s}),r=Object.assign({},t,{output:n});return Object.getOwnPropertyDescriptor(t,"output_text")||Im(r),Object.defineProperty(r,"output_parsed",{enumerable:!0,get(){for(const s of r.output)if(s.type==="message"){for(const i of s.content)if(i.type==="output_text"&&i.parsed!==null)return i.parsed}return null}}),r}function kv(t,e){var n,r,s,i;return((r=(n=t.text)==null?void 0:n.format)==null?void 0:r.type)!=="json_schema"?null:"$parseRaw"in((s=t.text)==null?void 0:s.format)?((i=t.text)==null?void 0:i.format).$parseRaw(e):JSON.parse(e)}function Cv(t){var e;return!!Ou((e=t.text)==null?void 0:e.format)}function Ev(t){return(t==null?void 0:t.$brand)==="auto-parseable-tool"}function Pv(t,e){return t.find(n=>n.type==="function"&&n.name===e)}function Rv(t,e){const n=Pv(t.tools??[],e.name);return{...e,...e,parsed_arguments:Ev(n)?n.$parseRaw(e.arguments):n!=null&&n.strict?JSON.parse(e.arguments):null}}function Im(t){const e=[];for(const n of t.output)if(n.type==="message")for(const r of n.content)r.type==="output_text"&&e.push(r.text);t.output_text=e.join("")}class bm extends b{list(e,n={},r){return Z(n)?this.list(e,{},n):this._client.getAPIList(`/responses/${e}/input_items`,Lv,{query:n,...r})}}var In=function(t,e,n,r,s){if(r==="m")throw new TypeError("Private method is not writable");if(r==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?t!==e||!s:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return r==="a"?s.call(t,n):s?s.value=n:e.set(t,n),n},Ft=function(t,e,n,r){if(n==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?t!==e||!r:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?r:n==="a"?r.call(t):r?r.value:e.get(t)},bn,si,Mt,ii,jf,Df,zf,Uf;class tc extends Ru{constructor(e){super(),bn.add(this),si.set(this,void 0),Mt.set(this,void 0),ii.set(this,void 0),In(this,si,e,"f")}static createResponse(e,n,r){const s=new tc(n);return s._run(()=>s._createOrRetrieveResponse(e,n,{...r,headers:{...r==null?void 0:r.headers,"X-Stainless-Helper-Method":"stream"}})),s}async _createOrRetrieveResponse(e,n,r){var o;const s=r==null?void 0:r.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),Ft(this,bn,"m",jf).call(this);let i,a=null;"response_id"in n?(i=await e.responses.retrieve(n.response_id,{stream:!0},{...r,signal:this.controller.signal,stream:!0}),a=n.starting_after??null):i=await e.responses.create({...n,stream:!0},{...r,signal:this.controller.signal}),this._connected();for await(const l of i)Ft(this,bn,"m",Df).call(this,l,a);if((o=i.controller.signal)!=null&&o.aborted)throw new Qe;return Ft(this,bn,"m",zf).call(this)}[(si=new WeakMap,Mt=new WeakMap,ii=new WeakMap,bn=new WeakSet,jf=function(){this.ended||In(this,Mt,void 0,"f")},Df=function(n,r){if(this.ended)return;const s=(a,o)=>{(r==null||o.sequence_number>r)&&this._emit(a,o)},i=Ft(this,bn,"m",Uf).call(this,n);switch(s("event",n),n.type){case"response.output_text.delta":{const a=i.output[n.output_index];if(!a)throw new I(`missing output at index ${n.output_index}`);if(a.type==="message"){const o=a.content[n.content_index];if(!o)throw new I(`missing content at index ${n.content_index}`);if(o.type!=="output_text")throw new I(`expected content to be 'output_text', got ${o.type}`);s("response.output_text.delta",{...n,snapshot:o.text})}break}case"response.function_call_arguments.delta":{const a=i.output[n.output_index];if(!a)throw new I(`missing output at index ${n.output_index}`);a.type==="function_call"&&s("response.function_call_arguments.delta",{...n,snapshot:a.arguments});break}default:s(n.type,n);break}},zf=function(){if(this.ended)throw new I("stream has ended, this shouldn't happen");const n=Ft(this,Mt,"f");if(!n)throw new I("request ended without sending any events");In(this,Mt,void 0,"f");const r=Nv(n,Ft(this,si,"f"));return In(this,ii,r,"f"),r},Uf=function(n){let r=Ft(this,Mt,"f");if(!r){if(n.type!=="response.created")throw new I(`When snapshot hasn't been set yet, expected 'response.created' event, got ${n.type}`);return r=In(this,Mt,n.response,"f"),r}switch(n.type){case"response.output_item.added":{r.output.push(n.item);break}case"response.content_part.added":{const s=r.output[n.output_index];if(!s)throw new I(`missing output at index ${n.output_index}`);s.type==="message"&&s.content.push(n.part);break}case"response.output_text.delta":{const s=r.output[n.output_index];if(!s)throw new I(`missing output at index ${n.output_index}`);if(s.type==="message"){const i=s.content[n.content_index];if(!i)throw new I(`missing content at index ${n.content_index}`);if(i.type!=="output_text")throw new I(`expected content to be 'output_text', got ${i.type}`);i.text+=n.delta}break}case"response.function_call_arguments.delta":{const s=r.output[n.output_index];if(!s)throw new I(`missing output at index ${n.output_index}`);s.type==="function_call"&&(s.arguments+=n.delta);break}case"response.completed":{In(this,Mt,n.response,"f");break}}return r},Symbol.asyncIterator)](){const e=[],n=[];let r=!1;return this.on("event",s=>{const i=n.shift();i?i.resolve(s):e.push(s)}),this.on("end",()=>{r=!0;for(const s of n)s.resolve(void 0);n.length=0}),this.on("abort",s=>{r=!0;for(const i of n)i.reject(s);n.length=0}),this.on("error",s=>{r=!0;for(const i of n)i.reject(s);n.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((i,a)=>n.push({resolve:i,reject:a})).then(i=>i?{value:i,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();const e=Ft(this,ii,"f");if(!e)throw new I("stream ended without producing a ChatCompletion");return e}}function Nv(t,e){return Sv(t,e)}class nc extends b{constructor(){super(...arguments),this.inputItems=new bm(this._client)}create(e,n){return this._client.post("/responses",{body:e,...n,stream:e.stream??!1})._thenUnwrap(r=>("object"in r&&r.object==="response"&&Im(r),r))}retrieve(e,n={},r){return this._client.get(`/responses/${e}`,{query:n,...r,stream:(n==null?void 0:n.stream)??!1})}del(e,n){return this._client.delete(`/responses/${e}`,{...n,headers:{Accept:"*/*",...n==null?void 0:n.headers}})}parse(e,n){return this._client.responses.create(e,n)._thenUnwrap(r=>Am(r,e))}stream(e,n){return tc.createResponse(this._client,e,n)}cancel(e,n){return this._client.post(`/responses/${e}/cancel`,{...n,headers:{Accept:"*/*",...n==null?void 0:n.headers}})}}class Lv extends oe{}nc.InputItems=bm;class Tm extends b{create(e,n,r){return this._client.post(`/uploads/${e}/parts`,En({body:n,...r}))}}class rc extends b{constructor(){super(...arguments),this.parts=new Tm(this._client)}create(e,n){return this._client.post("/uploads",{body:e,...n})}cancel(e,n){return this._client.post(`/uploads/${e}/cancel`,n)}complete(e,n,r){return this._client.post(`/uploads/${e}/complete`,{body:n,...r})}}rc.Parts=Tm;const Ov=async t=>{const e=await Promise.allSettled(t),n=e.filter(s=>s.status==="rejected");if(n.length){for(const s of n)console.error(s.reason);throw new Error(`${n.length} promise(s) failed - see the above errors`)}const r=[];for(const s of e)s.status==="fulfilled"&&r.push(s.value);return r};class Ia extends b{create(e,n,r){return this._client.post(`/vector_stores/${e}/files`,{body:n,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}retrieve(e,n,r){return this._client.get(`/vector_stores/${e}/files/${n}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}update(e,n,r,s){return this._client.post(`/vector_stores/${e}/files/${n}`,{body:r,...s,headers:{"OpenAI-Beta":"assistants=v2",...s==null?void 0:s.headers}})}list(e,n={},r){return Z(n)?this.list(e,{},n):this._client.getAPIList(`/vector_stores/${e}/files`,ba,{query:n,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}del(e,n,r){return this._client.delete(`/vector_stores/${e}/files/${n}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}async createAndPoll(e,n,r){const s=await this.create(e,n,r);return await this.poll(e,s.id,r)}async poll(e,n,r){const s={...r==null?void 0:r.headers,"X-Stainless-Poll-Helper":"true"};for(r!=null&&r.pollIntervalMs&&(s["X-Stainless-Custom-Poll-Interval"]=r.pollIntervalMs.toString());;){const i=await this.retrieve(e,n,{...r,headers:s}).withResponse(),a=i.data;switch(a.status){case"in_progress":let o=5e3;if(r!=null&&r.pollIntervalMs)o=r.pollIntervalMs;else{const l=i.response.headers.get("openai-poll-after-ms");if(l){const u=parseInt(l);isNaN(u)||(o=u)}}await Es(o);break;case"failed":case"completed":return a}}}async upload(e,n,r){const s=await this._client.files.create({file:n,purpose:"assistants"},r);return this.create(e,{file_id:s.id},r)}async uploadAndPoll(e,n,r){const s=await this.upload(e,n,r);return await this.poll(e,s.id,r)}content(e,n,r){return this._client.getAPIList(`/vector_stores/${e}/files/${n}/content`,sc,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}}class ba extends oe{}class sc extends Ea{}Ia.VectorStoreFilesPage=ba;Ia.FileContentResponsesPage=sc;class $m extends b{create(e,n,r){return this._client.post(`/vector_stores/${e}/file_batches`,{body:n,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}retrieve(e,n,r){return this._client.get(`/vector_stores/${e}/file_batches/${n}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}cancel(e,n,r){return this._client.post(`/vector_stores/${e}/file_batches/${n}/cancel`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}async createAndPoll(e,n,r){const s=await this.create(e,n);return await this.poll(e,s.id,r)}listFiles(e,n,r={},s){return Z(r)?this.listFiles(e,n,{},r):this._client.getAPIList(`/vector_stores/${e}/file_batches/${n}/files`,ba,{query:r,...s,headers:{"OpenAI-Beta":"assistants=v2",...s==null?void 0:s.headers}})}async poll(e,n,r){const s={...r==null?void 0:r.headers,"X-Stainless-Poll-Helper":"true"};for(r!=null&&r.pollIntervalMs&&(s["X-Stainless-Custom-Poll-Interval"]=r.pollIntervalMs.toString());;){const{data:i,response:a}=await this.retrieve(e,n,{...r,headers:s}).withResponse();switch(i.status){case"in_progress":let o=5e3;if(r!=null&&r.pollIntervalMs)o=r.pollIntervalMs;else{const l=a.headers.get("openai-poll-after-ms");if(l){const u=parseInt(l);isNaN(u)||(o=u)}}await Es(o);break;case"failed":case"cancelled":case"completed":return i}}}async uploadAndPoll(e,{files:n,fileIds:r=[]},s){if(n==null||n.length==0)throw new Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");const i=(s==null?void 0:s.maxConcurrency)??5,a=Math.min(i,n.length),o=this._client,l=n.values(),u=[...r];async function h(c){for(let g of c){const v=await o.files.create({file:g,purpose:"assistants"},s);u.push(v.id)}}const d=Array(a).fill(l).map(h);return await Ov(d),await this.createAndPoll(e,{file_ids:u})}}class an extends b{constructor(){super(...arguments),this.files=new Ia(this._client),this.fileBatches=new $m(this._client)}create(e,n){return this._client.post("/vector_stores",{body:e,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}retrieve(e,n){return this._client.get(`/vector_stores/${e}`,{...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}update(e,n,r){return this._client.post(`/vector_stores/${e}`,{body:n,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}list(e={},n){return Z(e)?this.list({},e):this._client.getAPIList("/vector_stores",ic,{query:e,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}del(e,n){return this._client.delete(`/vector_stores/${e}`,{...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}search(e,n,r){return this._client.getAPIList(`/vector_stores/${e}/search`,ac,{body:n,method:"post",...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}}class ic extends oe{}class ac extends Ea{}an.VectorStoresPage=ic;an.VectorStoreSearchResponsesPage=ac;an.Files=Ia;an.VectorStoreFilesPage=ba;an.FileContentResponsesPage=sc;an.FileBatches=$m;var Fm;class j extends Y0{constructor({baseURL:e=Zs("OPENAI_BASE_URL"),apiKey:n=Zs("OPENAI_API_KEY"),organization:r=Zs("OPENAI_ORG_ID")??null,project:s=Zs("OPENAI_PROJECT_ID")??null,...i}={}){if(n===void 0)throw new I("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");const a={apiKey:n,organization:r,project:s,...i,baseURL:e||"https://api.openai.com/v1"};if(!a.dangerouslyAllowBrowser&&ov())throw new I(`It looks like you're running in a browser-like environment.

This is disabled by default, as it risks exposing your secret API credentials to attackers.
If you understand the risks and have appropriate mitigations in place,
you can set the \`dangerouslyAllowBrowser\` option to \`true\`, e.g.,

new OpenAI({ apiKey, dangerouslyAllowBrowser: true });

https://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety
`);super({baseURL:a.baseURL,timeout:a.timeout??6e5,httpAgent:a.httpAgent,maxRetries:a.maxRetries,fetch:a.fetch}),this.completions=new km(this),this.chat=new Na(this),this.embeddings=new Em(this),this.files=new Vu(this),this.images=new Lm(this),this.audio=new Ps(this),this.moderations=new Om(this),this.models=new Zu(this),this.fineTuning=new sn(this),this.graders=new qu(this),this.vectorStores=new an(this),this.beta=new yr(this),this.batches=new Eu(this),this.uploads=new rc(this),this.responses=new nc(this),this.evals=new As(this),this.containers=new Ls(this),this._options=a,this.apiKey=n,this.organization=r,this.project=s}defaultQuery(){return this._options.defaultQuery}defaultHeaders(e){return{...super.defaultHeaders(e),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project,...this._options.defaultHeaders}}authHeaders(e){return{Authorization:`Bearer ${this.apiKey}`}}stringifyQuery(e){return b0(e,{arrayFormat:"brackets"})}}Fm=j;j.OpenAI=Fm;j.DEFAULT_TIMEOUT=6e5;j.OpenAIError=I;j.APIError=ye;j.APIConnectionError=_a;j.APIConnectionTimeoutError=Cu;j.APIUserAbortError=Qe;j.NotFoundError=Tp;j.ConflictError=$p;j.RateLimitError=Mp;j.BadRequestError=Ap;j.AuthenticationError=Ip;j.InternalServerError=jp;j.PermissionDeniedError=bp;j.UnprocessableEntityError=Fp;j.toFile=Hp;j.fileFromPath=Np;j.Completions=km;j.Chat=Na;j.ChatCompletionsPage=Ra;j.Embeddings=Em;j.Files=Vu;j.FileObjectsPage=Hu;j.Images=Lm;j.Audio=Ps;j.Moderations=Om;j.Models=Zu;j.ModelsPage=ec;j.FineTuning=sn;j.Graders=qu;j.VectorStores=an;j.VectorStoresPage=ic;j.VectorStoreSearchResponsesPage=ac;j.Beta=yr;j.Batches=Eu;j.BatchesPage=Pu;j.Uploads=rc;j.Responses=nc;j.Evals=As;j.EvalListResponsesPage=Bu;j.Containers=Ls;j.ContainerListResponsesPage=ju;const Av=new j({apiKey:"sk_AS0ZhtaINoaRD6SSP-DrW820Pq6iMmM_OgTjHGLKy00",baseURL:"https://api.novita.ai/v3/openai",dangerouslyAllowBrowser:!0});async function Iv(t,e,n){var r,s;try{const i=`You are a detective's assistant AI helping solve the following case:
Title: ${e.title}
Background: ${e.background}
Objective: ${e.objective}

Available clues:
${e.clues.map(o=>`- ${o}`).join(`
`)}

Characters involved:
${e.characters.map(o=>`- ${o.name}: ${o.description}`).join(`
`)}

Respond in the same language as the user's message. Keep responses concise and focused on helping solve the case. Don't reveal who is guilty directly, but guide the user towards the solution through careful analysis of clues and evidence.`;return((s=(r=(await Av.chat.completions.create({model:"deepseek/deepseek-r1-0528",messages:[{role:"system",content:i},...n,{role:"user",content:t}],temperature:.7,max_tokens:200})).choices[0])==null?void 0:r.message)==null?void 0:s.content)||"I apologize, but I couldn't generate a response at this time."}catch(i){return console.error("Error generating AI response:",i),"I apologize, but I'm having trouble connecting to the AI service at the moment."}}let ai;const bv=new Uint8Array(16);function Tv(){if(!ai&&(ai=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!ai))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return ai(bv)}const de=[];for(let t=0;t<256;++t)de.push((t+256).toString(16).slice(1));function $v(t,e=0){return de[t[e+0]]+de[t[e+1]]+de[t[e+2]]+de[t[e+3]]+"-"+de[t[e+4]]+de[t[e+5]]+"-"+de[t[e+6]]+de[t[e+7]]+"-"+de[t[e+8]]+de[t[e+9]]+"-"+de[t[e+10]]+de[t[e+11]]+de[t[e+12]]+de[t[e+13]]+de[t[e+14]]+de[t[e+15]]}const Fv=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),Bf={randomUUID:Fv};function oi(t,e,n){if(Bf.randomUUID&&!e&&!t)return Bf.randomUUID();t=t||{};const r=t.random||(t.rng||Tv)();return r[6]=r[6]&15|64,r[8]=r[8]&63|128,$v(r)}function Mv(){if(console&&console.warn){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];wn(e[0])&&(e[0]=`react-i18next:: ${e[0]}`),console.warn(...e)}}const Vf={};function Rl(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];wn(e[0])&&Vf[e[0]]||(wn(e[0])&&(Vf[e[0]]=new Date),Mv(...e))}const Mm=(t,e)=>()=>{if(t.isInitialized)e();else{const n=()=>{setTimeout(()=>{t.off("initialized",n)},0),e()};t.on("initialized",n)}},Hf=(t,e,n)=>{t.loadNamespaces(e,Mm(t,n))},Wf=(t,e,n,r)=>{wn(n)&&(n=[n]),n.forEach(s=>{t.options.ns.indexOf(s)<0&&t.options.ns.push(s)}),t.loadLanguages(e,Mm(t,r))},jv=function(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const r=e.languages[0],s=e.options?e.options.fallbackLng:!1,i=e.languages[e.languages.length-1];if(r.toLowerCase()==="cimode")return!0;const a=(o,l)=>{const u=e.services.backendConnector.state[`${o}|${l}`];return u===-1||u===2};return n.bindI18n&&n.bindI18n.indexOf("languageChanging")>-1&&e.services.backendConnector.backend&&e.isLanguageChangingTo&&!a(e.isLanguageChangingTo,t)?!1:!!(e.hasResourceBundle(r,t)||!e.services.backendConnector.backend||e.options.resources&&!e.options.partialBundledLanguages||a(r,t)&&(!s||a(i,t)))},Dv=function(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return!e.languages||!e.languages.length?(Rl("i18n.languages were undefined or empty",e.languages),!0):e.options.ignoreJSONStructure!==void 0?e.hasLoadedNamespace(t,{lng:n.lng,precheck:(s,i)=>{if(n.bindI18n&&n.bindI18n.indexOf("languageChanging")>-1&&s.services.backendConnector.backend&&s.isLanguageChangingTo&&!i(s.isLanguageChangingTo,t))return!1}}):jv(t,e,n)},wn=t=>typeof t=="string",zv=t=>typeof t=="object"&&t!==null,Uv=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,Bv={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},Vv=t=>Bv[t],Hv=t=>t.replace(Uv,Vv);let Nl={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:Hv};const Wv=function(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Nl={...Nl,...t}},Kv=()=>Nl;let jm;const Qv=t=>{jm=t},Jv=()=>jm,Xv={type:"3rdParty",init(t){Wv(t.options.react),Qv(t)}},Gv=V.createContext();class Yv{constructor(){oc(this,"getUsedNamespaces",()=>Object.keys(this.usedNamespaces));this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(n=>{this.usedNamespaces[n]||(this.usedNamespaces[n]=!0)})}}const qv=(t,e)=>{const n=V.useRef();return V.useEffect(()=>{n.current=t},[t,e]),n.current},Dm=(t,e,n,r)=>t.getFixedT(e,n,r),Zv=(t,e,n,r)=>V.useCallback(Dm(t,e,n,r),[t,e,n,r]),Ta=function(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const{i18n:n}=e,{i18n:r,defaultNS:s}=V.useContext(Gv)||{},i=n||r||Jv();if(i&&!i.reportNamespaces&&(i.reportNamespaces=new Yv),!i){Rl("You will need to pass in an i18next instance by using initReactI18next");const y=(S,x)=>wn(x)?x:zv(x)&&wn(x.defaultValue)?x.defaultValue:Array.isArray(S)?S[S.length-1]:S,k=[y,{},!1];return k.t=y,k.i18n={},k.ready=!1,k}i.options.react&&i.options.react.wait!==void 0&&Rl("It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const a={...Kv(),...i.options.react,...e},{useSuspense:o,keyPrefix:l}=a;let u=s||i.options&&i.options.defaultNS;u=wn(u)?[u]:u||["translation"],i.reportNamespaces.addUsedNamespaces&&i.reportNamespaces.addUsedNamespaces(u);const h=(i.isInitialized||i.initializedStoreOnce)&&u.every(y=>Dv(y,i,a)),d=Zv(i,e.lng||null,a.nsMode==="fallback"?u:u[0],l),c=()=>d,g=()=>Dm(i,e.lng||null,a.nsMode==="fallback"?u:u[0],l),[v,w]=V.useState(c);let _=u.join();e.lng&&(_=`${e.lng}${_}`);const m=qv(_),f=V.useRef(!0);V.useEffect(()=>{const{bindI18n:y,bindI18nStore:k}=a;f.current=!0,!h&&!o&&(e.lng?Wf(i,e.lng,u,()=>{f.current&&w(g)}):Hf(i,u,()=>{f.current&&w(g)})),h&&m&&m!==_&&f.current&&w(g);const S=()=>{f.current&&w(g)};return y&&i&&i.on(y,S),k&&i&&i.store.on(k,S),()=>{f.current=!1,y&&i&&y.split(" ").forEach(x=>i.off(x,S)),k&&i&&k.split(" ").forEach(x=>i.store.off(x,S))}},[i,_]),V.useEffect(()=>{f.current&&h&&w(c)},[i,l,h]);const p=[v,i,h];if(p.t=v,p.i18n=i,p.ready=h,h||!h&&!o)return p;throw new Promise(y=>{e.lng?Wf(i,e.lng,u,()=>y()):Hf(i,u,()=>y())})},zm=V.createContext(void 0),wr=()=>{const t=V.useContext(zm);if(!t)throw new Error("useCase must be used within a CaseProvider");return t},e1=({children:t})=>{const[e,n]=V.useState(null),[r,s]=V.useState(!0),[i,a]=V.useState([]),{t:o}=Ta();V.useEffect(()=>{l()},[]);const l=()=>{s(!0),setTimeout(()=>{const d=k0();n(d);const c=[{id:oi(),text:o("welcome",{title:d.title}),sender:"system",timestamp:new Date}];a(c),s(!1)},1e3)},h={currentCase:e,loading:r,messages:i,addMessage:async d=>{if(!d.trim()||!e)return;const c={id:oi(),text:d,sender:"user",timestamp:new Date};a(g=>[...g,c]);try{const g=i.map(_=>({role:_.sender==="user"?"user":"assistant",content:_.text})),v=await Iv(d,e,g),w={id:oi(),text:v,sender:"system",timestamp:new Date};a(_=>[..._,w])}catch(g){console.error("Error getting AI response:",g);const v={id:oi(),text:o("error.aiResponse"),sender:"system",timestamp:new Date};a(w=>[...w,v])}},startNewCase:l};return C.jsx(zm.Provider,{value:h,children:t})};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var t1={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n1=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),lt=(t,e)=>{const n=V.forwardRef(({color:r="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:a,className:o="",children:l,...u},h)=>V.createElement("svg",{ref:h,...t1,width:s,height:s,stroke:r,strokeWidth:a?Number(i)*24/Number(s):i,className:["lucide",`lucide-${n1(t)}`,o].join(" "),...u},[...e.map(([d,c])=>V.createElement(d,c)),...Array.isArray(l)?l:[l]]));return n.displayName=`${t}`,n};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const r1=lt("BadgeCheck",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Um=lt("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bm=lt("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const s1=lt("ClipboardList",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const i1=lt("FileSearch",[["path",{d:"M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v3",key:"1vg67v"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["circle",{cx:"5",cy:"14",r:"3",key:"ufru5t"}],["path",{d:"m9 18-1.5-1.5",key:"1j6qii"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a1=lt("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const o1=lt("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const l1=lt("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u1=lt("SendHorizontal",[["path",{d:"m3 3 3 9-3 9 19-9Z",key:"1aobqy"}],["path",{d:"M6 12h16",key:"s4cdu5"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c1=lt("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f1=lt("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),d1=()=>{const{startNewCase:t,loading:e}=wr(),{t:n,i18n:r}=Ta(),s=()=>{const i=r.language==="en"?"zh":"en";r.changeLanguage(i)};return C.jsx("header",{className:"bg-gradient-to-r from-gray-900 to-gray-800 shadow-md",children:C.jsxs("div",{className:"container mx-auto px-4 py-4 flex flex-col md:flex-row items-center justify-between",children:[C.jsxs("div",{className:"flex items-center mb-4 md:mb-0",children:[C.jsx(r1,{className:"text-amber-500 mr-2",size:32}),C.jsx("h1",{className:"text-2xl md:text-3xl font-serif font-bold text-white",children:n("title")})]}),C.jsxs("div",{className:"flex items-center space-x-4",children:[C.jsx("button",{onClick:s,className:"flex items-center text-white hover:text-amber-500 transition-colors duration-300",children:C.jsx(o1,{size:20})}),C.jsxs("button",{onClick:t,disabled:e,className:"flex items-center bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-md transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed",children:[C.jsx(i1,{className:"mr-2",size:18}),n("newCase")]})]})]})})},h1=()=>{const{currentCase:t}=wr();return t?C.jsx("div",{className:"bg-amber-50 border-l-4 border-amber-500 p-6 rounded-md shadow-md mb-6 transform transition-all duration-500 hover:shadow-lg",children:C.jsxs("div",{className:"flex items-start",children:[C.jsx(a1,{className:"text-amber-700 mr-3 mt-1 flex-shrink-0",size:24}),C.jsxs("div",{children:[C.jsx("h2",{className:"text-xl font-serif font-bold text-gray-800 mb-2",children:t.title}),C.jsxs("div",{className:"space-y-4",children:[C.jsxs("div",{children:[C.jsx("h3",{className:"text-md font-medium text-gray-700 mb-1",children:"Background:"}),C.jsx("p",{className:"text-gray-600",children:t.background})]}),C.jsxs("div",{children:[C.jsx("h3",{className:"text-md font-medium text-gray-700 mb-1",children:"Objective:"}),C.jsx("p",{className:"text-gray-600",children:t.objective})]})]})]})]})}):null},p1=()=>{const{currentCase:t}=wr(),[e,n]=V.useState(!0);return t?C.jsxs("div",{className:"bg-blue-50 border-l-4 border-blue-500 p-6 rounded-md shadow-md mb-6 transform transition-all duration-500 hover:shadow-lg",children:[C.jsxs("div",{className:"flex items-center justify-between cursor-pointer",onClick:()=>n(!e),children:[C.jsxs("div",{className:"flex items-center",children:[C.jsx(s1,{className:"text-blue-700 mr-3 flex-shrink-0",size:24}),C.jsx("h2",{className:"text-xl font-serif font-bold text-gray-800",children:"Evidence & Clues"})]}),e?C.jsx(Bm,{className:"text-gray-500",size:20}):C.jsx(Um,{className:"text-gray-500",size:20})]}),e&&C.jsx("div",{className:"mt-4 pl-9",children:C.jsx("ul",{className:"space-y-3",children:t.clues.map((r,s)=>C.jsx("li",{className:"text-gray-600 bg-white p-3 rounded border-l-2 border-blue-300 shadow-sm hover:shadow-md transition-shadow duration-300",children:r},s))})})]}):null},m1=()=>{const{currentCase:t}=wr(),[e,n]=V.useState(!0);return t?C.jsxs("div",{className:"bg-purple-50 border-l-4 border-purple-500 p-6 rounded-md shadow-md mb-6 transform transition-all duration-500 hover:shadow-lg",children:[C.jsxs("div",{className:"flex items-center justify-between cursor-pointer",onClick:()=>n(!e),children:[C.jsxs("div",{className:"flex items-center",children:[C.jsx(f1,{className:"text-purple-700 mr-3 flex-shrink-0",size:24}),C.jsx("h2",{className:"text-xl font-serif font-bold text-gray-800",children:"Suspects & Witnesses"})]}),e?C.jsx(Bm,{className:"text-gray-500",size:20}):C.jsx(Um,{className:"text-gray-500",size:20})]}),e&&C.jsx("div",{className:"mt-4 pl-9",children:C.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:t.characters.map((r,s)=>C.jsxs("div",{className:"bg-white p-4 rounded-md border-l-2 border-purple-300 shadow-sm hover:shadow-md transition-shadow duration-300",children:[C.jsx("h3",{className:"font-medium text-gray-800 mb-1",children:r.name}),C.jsx("p",{className:"text-gray-600 text-sm",children:r.description})]},s))})})]}):null},g1=()=>{const{messages:t,addMessage:e}=wr(),[n,r]=V.useState(""),s=V.useRef(null);V.useEffect(()=>{var o;(o=s.current)==null||o.scrollIntoView({behavior:"smooth"})},[t]);const i=o=>{o.preventDefault(),n.trim()&&(e(n),r(""))},a=o=>o.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});return C.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg shadow-md flex flex-col h-[400px] overflow-hidden",children:[C.jsxs("div",{className:"p-3 bg-gray-800 text-white font-medium flex items-center",children:[C.jsx(c1,{className:"text-amber-400 mr-2",size:18}),"Detective Assistant"]}),C.jsxs("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[t.map(o=>C.jsx("div",{className:`flex ${o.sender==="user"?"justify-end":"justify-start"}`,children:C.jsxs("div",{className:`max-w-[80%] rounded-lg p-3 ${o.sender==="user"?"bg-blue-600 text-white rounded-br-none":"bg-gray-200 text-gray-800 rounded-bl-none"}`,children:[C.jsx("div",{className:"mb-1",children:o.text}),C.jsx("div",{className:`text-xs ${o.sender==="user"?"text-blue-100":"text-gray-500"} text-right`,children:a(o.timestamp)})]})},o.id)),C.jsx("div",{ref:s})]}),C.jsx("form",{onSubmit:i,className:"p-3 border-t border-gray-200 bg-white",children:C.jsxs("div",{className:"flex items-center",children:[C.jsx("input",{type:"text",value:n,onChange:o=>r(o.target.value),placeholder:"Ask questions or share your theory...",className:"flex-1 border border-gray-300 rounded-l-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"}),C.jsx("button",{type:"submit",className:"bg-blue-600 hover:bg-blue-700 text-white rounded-r-lg px-4 py-2 transition-colors duration-300",children:C.jsx(u1,{size:20})})]})})]})},y1=()=>{const{t}=Ta();return C.jsx("footer",{className:"bg-gray-800 text-gray-300 py-6 mt-auto",children:C.jsx("div",{className:"container mx-auto px-4",children:C.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[C.jsx("div",{className:"mb-4 md:mb-0",children:C.jsx("p",{className:"text-sm",children:t("footer.rights",{year:new Date().getFullYear()})})}),C.jsxs("div",{className:"flex space-x-4",children:[C.jsx("a",{href:"#",className:"text-gray-400 hover:text-amber-500 transition-colors duration-300",children:t("footer.privacy")}),C.jsx("a",{href:"#",className:"text-gray-400 hover:text-amber-500 transition-colors duration-300",children:t("footer.terms")}),C.jsx("a",{href:"#",className:"text-gray-400 hover:text-amber-500 transition-colors duration-300",children:t("footer.contact")})]})]})})})},v1=()=>{const{t}=Ta();return C.jsx("div",{className:"fixed inset-0 bg-gray-900 bg-opacity-90 flex items-center justify-center z-50",children:C.jsxs("div",{className:"text-center p-8 rounded-lg",children:[C.jsx("div",{className:"flex justify-center mb-6",children:C.jsxs("div",{className:"relative",children:[C.jsx(l1,{className:"text-amber-500 animate-pulse",size:64}),C.jsx("div",{className:"absolute top-0 left-0 w-full h-full flex items-center justify-center",children:C.jsx("div",{className:"w-8 h-8 border-t-2 border-b-2 border-amber-300 rounded-full animate-spin"})})]})}),C.jsx("h2",{className:"text-2xl font-serif font-bold text-white mb-2",children:t("loading.title")}),C.jsx("p",{className:"text-gray-300",children:t("loading.subtitle")})]})})},w1=()=>{const{loading:t}=wr();return t?C.jsx(v1,{}):C.jsx("div",{className:"flex-1",children:C.jsx("div",{className:"container mx-auto px-4 py-8",children:C.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[C.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[C.jsx(h1,{}),C.jsx(p1,{}),C.jsx(m1,{})]}),C.jsx("div",{children:C.jsx("div",{className:"sticky top-6",children:C.jsx(g1,{})})})]})})})};function x1(){return C.jsx(e1,{children:C.jsxs("div",{className:"flex flex-col min-h-screen bg-gray-100 bg-[url('https://www.transparenttextures.com/patterns/notebook-dark.png')]",children:[C.jsx(d1,{}),C.jsx(w1,{}),C.jsx(y1,{})]})})}const $=t=>typeof t=="string",Nr=()=>{let t,e;const n=new Promise((r,s)=>{t=r,e=s});return n.resolve=t,n.reject=e,n},Kf=t=>t==null?"":""+t,_1=(t,e,n)=>{t.forEach(r=>{e[r]&&(n[r]=e[r])})},S1=/###/g,Qf=t=>t&&t.indexOf("###")>-1?t.replace(S1,"."):t,Jf=t=>!t||$(t),qr=(t,e,n)=>{const r=$(e)?e.split("."):e;let s=0;for(;s<r.length-1;){if(Jf(t))return{};const i=Qf(r[s]);!t[i]&&n&&(t[i]=new n),Object.prototype.hasOwnProperty.call(t,i)?t=t[i]:t={},++s}return Jf(t)?{}:{obj:t,k:Qf(r[s])}},Xf=(t,e,n)=>{const{obj:r,k:s}=qr(t,e,Object);if(r!==void 0||e.length===1){r[s]=n;return}let i=e[e.length-1],a=e.slice(0,e.length-1),o=qr(t,a,Object);for(;o.obj===void 0&&a.length;)i=`${a[a.length-1]}.${i}`,a=a.slice(0,a.length-1),o=qr(t,a,Object),o&&o.obj&&typeof o.obj[`${o.k}.${i}`]<"u"&&(o.obj=void 0);o.obj[`${o.k}.${i}`]=n},k1=(t,e,n,r)=>{const{obj:s,k:i}=qr(t,e,Object);s[i]=s[i]||[],s[i].push(n)},ta=(t,e)=>{const{obj:n,k:r}=qr(t,e);if(n)return n[r]},C1=(t,e,n)=>{const r=ta(t,n);return r!==void 0?r:ta(e,n)},Vm=(t,e,n)=>{for(const r in e)r!=="__proto__"&&r!=="constructor"&&(r in t?$(t[r])||t[r]instanceof String||$(e[r])||e[r]instanceof String?n&&(t[r]=e[r]):Vm(t[r],e[r],n):t[r]=e[r]);return t},Tn=t=>t.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var E1={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const P1=t=>$(t)?t.replace(/[&<>"'\/]/g,e=>E1[e]):t;class R1{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const n=this.regExpMap.get(e);if(n!==void 0)return n;const r=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,r),this.regExpQueue.push(e),r}}const N1=[" ",",","?","!",";"],L1=new R1(20),O1=(t,e,n)=>{e=e||"",n=n||"";const r=N1.filter(a=>e.indexOf(a)<0&&n.indexOf(a)<0);if(r.length===0)return!0;const s=L1.getRegExp(`(${r.map(a=>a==="?"?"\\?":a).join("|")})`);let i=!s.test(t);if(!i){const a=t.indexOf(n);a>0&&!s.test(t.substring(0,a))&&(i=!0)}return i},Ll=function(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:".";if(!t)return;if(t[e])return t[e];const r=e.split(n);let s=t;for(let i=0;i<r.length;){if(!s||typeof s!="object")return;let a,o="";for(let l=i;l<r.length;++l)if(l!==i&&(o+=n),o+=r[l],a=s[o],a!==void 0){if(["string","number","boolean"].indexOf(typeof a)>-1&&l<r.length-1)continue;i+=l-i+1;break}s=a}return s},na=t=>t&&t.replace("_","-"),A1={type:"logger",log(t){this.output("log",t)},warn(t){this.output("warn",t)},error(t){this.output("error",t)},output(t,e){console&&console[t]&&console[t].apply(console,e)}};class ra{constructor(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.init(e,n)}init(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.prefix=n.prefix||"i18next:",this.logger=e||A1,this.options=n,this.debug=n.debug}log(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return this.forward(n,"log","",!0)}warn(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return this.forward(n,"warn","",!0)}error(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return this.forward(n,"error","")}deprecate(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return this.forward(n,"warn","WARNING DEPRECATED: ",!0)}forward(e,n,r,s){return s&&!this.debug?null:($(e[0])&&(e[0]=`${r}${this.prefix} ${e[0]}`),this.logger[n](e))}create(e){return new ra(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return e=e||this.options,e.prefix=e.prefix||this.prefix,new ra(this.logger,e)}}var vt=new ra;class $a{constructor(){this.observers={}}on(e,n){return e.split(" ").forEach(r=>{this.observers[r]||(this.observers[r]=new Map);const s=this.observers[r].get(n)||0;this.observers[r].set(n,s+1)}),this}off(e,n){if(this.observers[e]){if(!n){delete this.observers[e];return}this.observers[e].delete(n)}}emit(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),s=1;s<n;s++)r[s-1]=arguments[s];this.observers[e]&&Array.from(this.observers[e].entries()).forEach(a=>{let[o,l]=a;for(let u=0;u<l;u++)o(...r)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(a=>{let[o,l]=a;for(let u=0;u<l;u++)o.apply(o,[e,...r])})}}class Gf extends $a{constructor(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=n,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const n=this.options.ns.indexOf(e);n>-1&&this.options.ns.splice(n,1)}getResource(e,n,r){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};const i=s.keySeparator!==void 0?s.keySeparator:this.options.keySeparator,a=s.ignoreJSONStructure!==void 0?s.ignoreJSONStructure:this.options.ignoreJSONStructure;let o;e.indexOf(".")>-1?o=e.split("."):(o=[e,n],r&&(Array.isArray(r)?o.push(...r):$(r)&&i?o.push(...r.split(i)):o.push(r)));const l=ta(this.data,o);return!l&&!n&&!r&&e.indexOf(".")>-1&&(e=o[0],n=o[1],r=o.slice(2).join(".")),l||!a||!$(r)?l:Ll(this.data&&this.data[e]&&this.data[e][n],r,i)}addResource(e,n,r,s){let i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{silent:!1};const a=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator;let o=[e,n];r&&(o=o.concat(a?r.split(a):r)),e.indexOf(".")>-1&&(o=e.split("."),s=n,n=o[1]),this.addNamespaces(n),Xf(this.data,o,s),i.silent||this.emit("added",e,n,r,s)}addResources(e,n,r){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{silent:!1};for(const i in r)($(r[i])||Array.isArray(r[i]))&&this.addResource(e,n,i,r[i],{silent:!0});s.silent||this.emit("added",e,n,r)}addResourceBundle(e,n,r,s,i){let a=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{silent:!1,skipCopy:!1},o=[e,n];e.indexOf(".")>-1&&(o=e.split("."),s=r,r=n,n=o[1]),this.addNamespaces(n);let l=ta(this.data,o)||{};a.skipCopy||(r=JSON.parse(JSON.stringify(r))),s?Vm(l,r,i):l={...l,...r},Xf(this.data,o,l),a.silent||this.emit("added",e,n,r)}removeResourceBundle(e,n){this.hasResourceBundle(e,n)&&delete this.data[e][n],this.removeNamespaces(n),this.emit("removed",e,n)}hasResourceBundle(e,n){return this.getResource(e,n)!==void 0}getResourceBundle(e,n){return n||(n=this.options.defaultNS),this.options.compatibilityAPI==="v1"?{...this.getResource(e,n)}:this.getResource(e,n)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const n=this.getDataByLanguage(e);return!!(n&&Object.keys(n)||[]).find(s=>n[s]&&Object.keys(n[s]).length>0)}toJSON(){return this.data}}var Hm={processors:{},addPostProcessor(t){this.processors[t.name]=t},handle(t,e,n,r,s){return t.forEach(i=>{this.processors[i]&&(e=this.processors[i].process(e,n,r,s))}),e}};const Yf={};class sa extends $a{constructor(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),_1(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=n,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=vt.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}};if(e==null)return!1;const r=this.resolve(e,n);return r&&r.res!==void 0}extractFromKey(e,n){let r=n.nsSeparator!==void 0?n.nsSeparator:this.options.nsSeparator;r===void 0&&(r=":");const s=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator;let i=n.ns||this.options.defaultNS||[];const a=r&&e.indexOf(r)>-1,o=!this.options.userDefinedKeySeparator&&!n.keySeparator&&!this.options.userDefinedNsSeparator&&!n.nsSeparator&&!O1(e,r,s);if(a&&!o){const l=e.match(this.interpolator.nestingRegexp);if(l&&l.length>0)return{key:e,namespaces:$(i)?[i]:i};const u=e.split(r);(r!==s||r===s&&this.options.ns.indexOf(u[0])>-1)&&(i=u.shift()),e=u.join(s)}return{key:e,namespaces:$(i)?[i]:i}}translate(e,n,r){if(typeof n!="object"&&this.options.overloadTranslationOptionHandler&&(n=this.options.overloadTranslationOptionHandler(arguments)),typeof n=="object"&&(n={...n}),n||(n={}),e==null)return"";Array.isArray(e)||(e=[String(e)]);const s=n.returnDetails!==void 0?n.returnDetails:this.options.returnDetails,i=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator,{key:a,namespaces:o}=this.extractFromKey(e[e.length-1],n),l=o[o.length-1],u=n.lng||this.language,h=n.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(u&&u.toLowerCase()==="cimode"){if(h){const y=n.nsSeparator||this.options.nsSeparator;return s?{res:`${l}${y}${a}`,usedKey:a,exactUsedKey:a,usedLng:u,usedNS:l,usedParams:this.getUsedParamsDetails(n)}:`${l}${y}${a}`}return s?{res:a,usedKey:a,exactUsedKey:a,usedLng:u,usedNS:l,usedParams:this.getUsedParamsDetails(n)}:a}const d=this.resolve(e,n);let c=d&&d.res;const g=d&&d.usedKey||a,v=d&&d.exactUsedKey||a,w=Object.prototype.toString.apply(c),_=["[object Number]","[object Function]","[object RegExp]"],m=n.joinArrays!==void 0?n.joinArrays:this.options.joinArrays,f=!this.i18nFormat||this.i18nFormat.handleAsObject,p=!$(c)&&typeof c!="boolean"&&typeof c!="number";if(f&&c&&p&&_.indexOf(w)<0&&!($(m)&&Array.isArray(c))){if(!n.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const y=this.options.returnedObjectHandler?this.options.returnedObjectHandler(g,c,{...n,ns:o}):`key '${a} (${this.language})' returned an object instead of string.`;return s?(d.res=y,d.usedParams=this.getUsedParamsDetails(n),d):y}if(i){const y=Array.isArray(c),k=y?[]:{},S=y?v:g;for(const x in c)if(Object.prototype.hasOwnProperty.call(c,x)){const R=`${S}${i}${x}`;k[x]=this.translate(R,{...n,joinArrays:!1,ns:o}),k[x]===R&&(k[x]=c[x])}c=k}}else if(f&&$(m)&&Array.isArray(c))c=c.join(m),c&&(c=this.extendTranslation(c,e,n,r));else{let y=!1,k=!1;const S=n.count!==void 0&&!$(n.count),x=sa.hasDefaultValue(n),R=S?this.pluralResolver.getSuffix(u,n.count,n):"",F=n.ordinal&&S?this.pluralResolver.getSuffix(u,n.count,{ordinal:!1}):"",L=S&&!n.ordinal&&n.count===0&&this.pluralResolver.shouldUseIntlApi(),T=L&&n[`defaultValue${this.options.pluralSeparator}zero`]||n[`defaultValue${R}`]||n[`defaultValue${F}`]||n.defaultValue;!this.isValidLookup(c)&&x&&(y=!0,c=T),this.isValidLookup(c)||(k=!0,c=a);const Ye=(n.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&k?void 0:c,ut=x&&T!==c&&this.options.updateMissing;if(k||y||ut){if(this.logger.log(ut?"updateKey":"missingKey",u,l,a,ut?T:c),i){const P=this.resolve(a,{...n,keySeparator:!1});P&&P.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let ct=[];const bt=this.languageUtils.getFallbackCodes(this.options.fallbackLng,n.lng||this.language);if(this.options.saveMissingTo==="fallback"&&bt&&bt[0])for(let P=0;P<bt.length;P++)ct.push(bt[P]);else this.options.saveMissingTo==="all"?ct=this.languageUtils.toResolveHierarchy(n.lng||this.language):ct.push(n.lng||this.language);const Nn=(P,A,M)=>{const Q=x&&M!==c?M:Ye;this.options.missingKeyHandler?this.options.missingKeyHandler(P,l,A,Q,ut,n):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(P,l,A,Q,ut,n),this.emit("missingKey",P,l,A,c)};this.options.saveMissing&&(this.options.saveMissingPlurals&&S?ct.forEach(P=>{const A=this.pluralResolver.getSuffixes(P,n);L&&n[`defaultValue${this.options.pluralSeparator}zero`]&&A.indexOf(`${this.options.pluralSeparator}zero`)<0&&A.push(`${this.options.pluralSeparator}zero`),A.forEach(M=>{Nn([P],a+M,n[`defaultValue${M}`]||T)})}):Nn(ct,a,T))}c=this.extendTranslation(c,e,n,d,r),k&&c===a&&this.options.appendNamespaceToMissingKey&&(c=`${l}:${a}`),(k||y)&&this.options.parseMissingKeyHandler&&(this.options.compatibilityAPI!=="v1"?c=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${l}:${a}`:a,y?c:void 0):c=this.options.parseMissingKeyHandler(c))}return s?(d.res=c,d.usedParams=this.getUsedParamsDetails(n),d):c}extendTranslation(e,n,r,s,i){var a=this;if(this.i18nFormat&&this.i18nFormat.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...r},r.lng||this.language||s.usedLng,s.usedNS,s.usedKey,{resolved:s});else if(!r.skipInterpolation){r.interpolation&&this.interpolator.init({...r,interpolation:{...this.options.interpolation,...r.interpolation}});const u=$(e)&&(r&&r.interpolation&&r.interpolation.skipOnVariables!==void 0?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let h;if(u){const c=e.match(this.interpolator.nestingRegexp);h=c&&c.length}let d=r.replace&&!$(r.replace)?r.replace:r;if(this.options.interpolation.defaultVariables&&(d={...this.options.interpolation.defaultVariables,...d}),e=this.interpolator.interpolate(e,d,r.lng||this.language||s.usedLng,r),u){const c=e.match(this.interpolator.nestingRegexp),g=c&&c.length;h<g&&(r.nest=!1)}!r.lng&&this.options.compatibilityAPI!=="v1"&&s&&s.res&&(r.lng=this.language||s.usedLng),r.nest!==!1&&(e=this.interpolator.nest(e,function(){for(var c=arguments.length,g=new Array(c),v=0;v<c;v++)g[v]=arguments[v];return i&&i[0]===g[0]&&!r.context?(a.logger.warn(`It seems you are nesting recursively key: ${g[0]} in key: ${n[0]}`),null):a.translate(...g,n)},r)),r.interpolation&&this.interpolator.reset()}const o=r.postProcess||this.options.postProcess,l=$(o)?[o]:o;return e!=null&&l&&l.length&&r.applyPostProcessor!==!1&&(e=Hm.handle(l,e,n,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...s,usedParams:this.getUsedParamsDetails(r)},...r}:r,this)),e}resolve(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r,s,i,a,o;return $(e)&&(e=[e]),e.forEach(l=>{if(this.isValidLookup(r))return;const u=this.extractFromKey(l,n),h=u.key;s=h;let d=u.namespaces;this.options.fallbackNS&&(d=d.concat(this.options.fallbackNS));const c=n.count!==void 0&&!$(n.count),g=c&&!n.ordinal&&n.count===0&&this.pluralResolver.shouldUseIntlApi(),v=n.context!==void 0&&($(n.context)||typeof n.context=="number")&&n.context!=="",w=n.lngs?n.lngs:this.languageUtils.toResolveHierarchy(n.lng||this.language,n.fallbackLng);d.forEach(_=>{this.isValidLookup(r)||(o=_,!Yf[`${w[0]}-${_}`]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(o)&&(Yf[`${w[0]}-${_}`]=!0,this.logger.warn(`key "${s}" for languages "${w.join(", ")}" won't get resolved as namespace "${o}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),w.forEach(m=>{if(this.isValidLookup(r))return;a=m;const f=[h];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(f,h,m,_,n);else{let y;c&&(y=this.pluralResolver.getSuffix(m,n.count,n));const k=`${this.options.pluralSeparator}zero`,S=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(c&&(f.push(h+y),n.ordinal&&y.indexOf(S)===0&&f.push(h+y.replace(S,this.options.pluralSeparator)),g&&f.push(h+k)),v){const x=`${h}${this.options.contextSeparator}${n.context}`;f.push(x),c&&(f.push(x+y),n.ordinal&&y.indexOf(S)===0&&f.push(x+y.replace(S,this.options.pluralSeparator)),g&&f.push(x+k))}}let p;for(;p=f.pop();)this.isValidLookup(r)||(i=p,r=this.getResource(m,_,p,n))}))})}),{res:r,usedKey:s,exactUsedKey:i,usedLng:a,usedNS:o}}isValidLookup(e){return e!==void 0&&!(!this.options.returnNull&&e===null)&&!(!this.options.returnEmptyString&&e==="")}getResource(e,n,r){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(e,n,r,s):this.resourceStore.getResource(e,n,r,s)}getUsedParamsDetails(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const n=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],r=e.replace&&!$(e.replace);let s=r?e.replace:e;if(r&&typeof e.count<"u"&&(s.count=e.count),this.options.interpolation.defaultVariables&&(s={...this.options.interpolation.defaultVariables,...s}),!r){s={...s};for(const i of n)delete s[i]}return s}static hasDefaultValue(e){const n="defaultValue";for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&n===r.substring(0,n.length)&&e[r]!==void 0)return!0;return!1}}const xo=t=>t.charAt(0).toUpperCase()+t.slice(1);class qf{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=vt.create("languageUtils")}getScriptPartFromCode(e){if(e=na(e),!e||e.indexOf("-")<0)return null;const n=e.split("-");return n.length===2||(n.pop(),n[n.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(n.join("-"))}getLanguagePartFromCode(e){if(e=na(e),!e||e.indexOf("-")<0)return e;const n=e.split("-");return this.formatLanguageCode(n[0])}formatLanguageCode(e){if($(e)&&e.indexOf("-")>-1){if(typeof Intl<"u"&&typeof Intl.getCanonicalLocales<"u")try{let s=Intl.getCanonicalLocales(e)[0];if(s&&this.options.lowerCaseLng&&(s=s.toLowerCase()),s)return s}catch{}const n=["hans","hant","latn","cyrl","cans","mong","arab"];let r=e.split("-");return this.options.lowerCaseLng?r=r.map(s=>s.toLowerCase()):r.length===2?(r[0]=r[0].toLowerCase(),r[1]=r[1].toUpperCase(),n.indexOf(r[1].toLowerCase())>-1&&(r[1]=xo(r[1].toLowerCase()))):r.length===3&&(r[0]=r[0].toLowerCase(),r[1].length===2&&(r[1]=r[1].toUpperCase()),r[0]!=="sgn"&&r[2].length===2&&(r[2]=r[2].toUpperCase()),n.indexOf(r[1].toLowerCase())>-1&&(r[1]=xo(r[1].toLowerCase())),n.indexOf(r[2].toLowerCase())>-1&&(r[2]=xo(r[2].toLowerCase()))),r.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let n;return e.forEach(r=>{if(n)return;const s=this.formatLanguageCode(r);(!this.options.supportedLngs||this.isSupportedCode(s))&&(n=s)}),!n&&this.options.supportedLngs&&e.forEach(r=>{if(n)return;const s=this.getLanguagePartFromCode(r);if(this.isSupportedCode(s))return n=s;n=this.options.supportedLngs.find(i=>{if(i===s)return i;if(!(i.indexOf("-")<0&&s.indexOf("-")<0)&&(i.indexOf("-")>0&&s.indexOf("-")<0&&i.substring(0,i.indexOf("-"))===s||i.indexOf(s)===0&&s.length>1))return i})}),n||(n=this.getFallbackCodes(this.options.fallbackLng)[0]),n}getFallbackCodes(e,n){if(!e)return[];if(typeof e=="function"&&(e=e(n)),$(e)&&(e=[e]),Array.isArray(e))return e;if(!n)return e.default||[];let r=e[n];return r||(r=e[this.getScriptPartFromCode(n)]),r||(r=e[this.formatLanguageCode(n)]),r||(r=e[this.getLanguagePartFromCode(n)]),r||(r=e.default),r||[]}toResolveHierarchy(e,n){const r=this.getFallbackCodes(n||this.options.fallbackLng||[],e),s=[],i=a=>{a&&(this.isSupportedCode(a)?s.push(a):this.logger.warn(`rejecting language code not found in supportedLngs: ${a}`))};return $(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&i(this.formatLanguageCode(e)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&i(this.getScriptPartFromCode(e)),this.options.load!=="currentOnly"&&i(this.getLanguagePartFromCode(e))):$(e)&&i(this.formatLanguageCode(e)),r.forEach(a=>{s.indexOf(a)<0&&i(this.formatLanguageCode(a))}),s}}let I1=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],b1={1:t=>+(t>1),2:t=>+(t!=1),3:t=>0,4:t=>t%10==1&&t%100!=11?0:t%10>=2&&t%10<=4&&(t%100<10||t%100>=20)?1:2,5:t=>t==0?0:t==1?1:t==2?2:t%100>=3&&t%100<=10?3:t%100>=11?4:5,6:t=>t==1?0:t>=2&&t<=4?1:2,7:t=>t==1?0:t%10>=2&&t%10<=4&&(t%100<10||t%100>=20)?1:2,8:t=>t==1?0:t==2?1:t!=8&&t!=11?2:3,9:t=>+(t>=2),10:t=>t==1?0:t==2?1:t<7?2:t<11?3:4,11:t=>t==1||t==11?0:t==2||t==12?1:t>2&&t<20?2:3,12:t=>+(t%10!=1||t%100==11),13:t=>+(t!==0),14:t=>t==1?0:t==2?1:t==3?2:3,15:t=>t%10==1&&t%100!=11?0:t%10>=2&&(t%100<10||t%100>=20)?1:2,16:t=>t%10==1&&t%100!=11?0:t!==0?1:2,17:t=>t==1||t%10==1&&t%100!=11?0:1,18:t=>t==0?0:t==1?1:2,19:t=>t==1?0:t==0||t%100>1&&t%100<11?1:t%100>10&&t%100<20?2:3,20:t=>t==1?0:t==0||t%100>0&&t%100<20?1:2,21:t=>t%100==1?1:t%100==2?2:t%100==3||t%100==4?3:0,22:t=>t==1?0:t==2?1:(t<0||t>10)&&t%10==0?2:3};const T1=["v1","v2","v3"],$1=["v4"],Zf={zero:0,one:1,two:2,few:3,many:4,other:5},F1=()=>{const t={};return I1.forEach(e=>{e.lngs.forEach(n=>{t[n]={numbers:e.nr,plurals:b1[e.fc]}})}),t};class M1{constructor(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.languageUtils=e,this.options=n,this.logger=vt.create("pluralResolver"),(!this.options.compatibilityJSON||$1.includes(this.options.compatibilityJSON))&&(typeof Intl>"u"||!Intl.PluralRules)&&(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=F1(),this.pluralRulesCache={}}addRule(e,n){this.rules[e]=n}clearCache(){this.pluralRulesCache={}}getRule(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.shouldUseIntlApi()){const r=na(e==="dev"?"en":e),s=n.ordinal?"ordinal":"cardinal",i=JSON.stringify({cleanedCode:r,type:s});if(i in this.pluralRulesCache)return this.pluralRulesCache[i];let a;try{a=new Intl.PluralRules(r,{type:s})}catch{if(!e.match(/-|_/))return;const l=this.languageUtils.getLanguagePartFromCode(e);a=this.getRule(l,n)}return this.pluralRulesCache[i]=a,a}return this.rules[e]||this.rules[this.languageUtils.getLanguagePartFromCode(e)]}needsPlural(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const r=this.getRule(e,n);return this.shouldUseIntlApi()?r&&r.resolvedOptions().pluralCategories.length>1:r&&r.numbers.length>1}getPluralFormsOfKey(e,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.getSuffixes(e,r).map(s=>`${n}${s}`)}getSuffixes(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const r=this.getRule(e,n);return r?this.shouldUseIntlApi()?r.resolvedOptions().pluralCategories.sort((s,i)=>Zf[s]-Zf[i]).map(s=>`${this.options.prepend}${n.ordinal?`ordinal${this.options.prepend}`:""}${s}`):r.numbers.map(s=>this.getSuffix(e,s,n)):[]}getSuffix(e,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const s=this.getRule(e,r);return s?this.shouldUseIntlApi()?`${this.options.prepend}${r.ordinal?`ordinal${this.options.prepend}`:""}${s.select(n)}`:this.getSuffixRetroCompatible(s,n):(this.logger.warn(`no plural rule found for: ${e}`),"")}getSuffixRetroCompatible(e,n){const r=e.noAbs?e.plurals(n):e.plurals(Math.abs(n));let s=e.numbers[r];this.options.simplifyPluralSuffix&&e.numbers.length===2&&e.numbers[0]===1&&(s===2?s="plural":s===1&&(s=""));const i=()=>this.options.prepend&&s.toString()?this.options.prepend+s.toString():s.toString();return this.options.compatibilityJSON==="v1"?s===1?"":typeof s=="number"?`_plural_${s.toString()}`:i():this.options.compatibilityJSON==="v2"||this.options.simplifyPluralSuffix&&e.numbers.length===2&&e.numbers[0]===1?i():this.options.prepend&&r.toString()?this.options.prepend+r.toString():r.toString()}shouldUseIntlApi(){return!T1.includes(this.options.compatibilityJSON)}}const ed=function(t,e,n){let r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:".",s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=C1(t,e,n);return!i&&s&&$(n)&&(i=Ll(t,n,r),i===void 0&&(i=Ll(e,n,r))),i},_o=t=>t.replace(/\$/g,"$$$$");class j1{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=vt.create("interpolator"),this.options=e,this.format=e.interpolation&&e.interpolation.format||(n=>n),this.init(e)}init(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});const{escape:n,escapeValue:r,useRawValueToEscape:s,prefix:i,prefixEscaped:a,suffix:o,suffixEscaped:l,formatSeparator:u,unescapeSuffix:h,unescapePrefix:d,nestingPrefix:c,nestingPrefixEscaped:g,nestingSuffix:v,nestingSuffixEscaped:w,nestingOptionsSeparator:_,maxReplaces:m,alwaysFormat:f}=e.interpolation;this.escape=n!==void 0?n:P1,this.escapeValue=r!==void 0?r:!0,this.useRawValueToEscape=s!==void 0?s:!1,this.prefix=i?Tn(i):a||"{{",this.suffix=o?Tn(o):l||"}}",this.formatSeparator=u||",",this.unescapePrefix=h?"":d||"-",this.unescapeSuffix=this.unescapePrefix?"":h||"",this.nestingPrefix=c?Tn(c):g||Tn("$t("),this.nestingSuffix=v?Tn(v):w||Tn(")"),this.nestingOptionsSeparator=_||",",this.maxReplaces=m||1e3,this.alwaysFormat=f!==void 0?f:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(n,r)=>n&&n.source===r?(n.lastIndex=0,n):new RegExp(r,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,n,r,s){let i,a,o;const l=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},u=g=>{if(g.indexOf(this.formatSeparator)<0){const m=ed(n,l,g,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(m,void 0,r,{...s,...n,interpolationkey:g}):m}const v=g.split(this.formatSeparator),w=v.shift().trim(),_=v.join(this.formatSeparator).trim();return this.format(ed(n,l,w,this.options.keySeparator,this.options.ignoreJSONStructure),_,r,{...s,...n,interpolationkey:w})};this.resetRegExp();const h=s&&s.missingInterpolationHandler||this.options.missingInterpolationHandler,d=s&&s.interpolation&&s.interpolation.skipOnVariables!==void 0?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:g=>_o(g)},{regex:this.regexp,safeValue:g=>this.escapeValue?_o(this.escape(g)):_o(g)}].forEach(g=>{for(o=0;i=g.regex.exec(e);){const v=i[1].trim();if(a=u(v),a===void 0)if(typeof h=="function"){const _=h(e,i,s);a=$(_)?_:""}else if(s&&Object.prototype.hasOwnProperty.call(s,v))a="";else if(d){a=i[0];continue}else this.logger.warn(`missed to pass in variable ${v} for interpolating ${e}`),a="";else!$(a)&&!this.useRawValueToEscape&&(a=Kf(a));const w=g.safeValue(a);if(e=e.replace(i[0],w),d?(g.regex.lastIndex+=a.length,g.regex.lastIndex-=i[0].length):g.regex.lastIndex=0,o++,o>=this.maxReplaces)break}}),e}nest(e,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},s,i,a;const o=(l,u)=>{const h=this.nestingOptionsSeparator;if(l.indexOf(h)<0)return l;const d=l.split(new RegExp(`${h}[ ]*{`));let c=`{${d[1]}`;l=d[0],c=this.interpolate(c,a);const g=c.match(/'/g),v=c.match(/"/g);(g&&g.length%2===0&&!v||v.length%2!==0)&&(c=c.replace(/'/g,'"'));try{a=JSON.parse(c),u&&(a={...u,...a})}catch(w){return this.logger.warn(`failed parsing options string in nesting for key ${l}`,w),`${l}${h}${c}`}return a.defaultValue&&a.defaultValue.indexOf(this.prefix)>-1&&delete a.defaultValue,l};for(;s=this.nestingRegexp.exec(e);){let l=[];a={...r},a=a.replace&&!$(a.replace)?a.replace:a,a.applyPostProcessor=!1,delete a.defaultValue;let u=!1;if(s[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(s[1])){const h=s[1].split(this.formatSeparator).map(d=>d.trim());s[1]=h.shift(),l=h,u=!0}if(i=n(o.call(this,s[1].trim(),a),a),i&&s[0]===e&&!$(i))return i;$(i)||(i=Kf(i)),i||(this.logger.warn(`missed to resolve ${s[1]} for nesting ${e}`),i=""),u&&(i=l.reduce((h,d)=>this.format(h,d,r.lng,{...r,interpolationkey:s[1].trim()}),i.trim())),e=e.replace(s[0],i),this.regexp.lastIndex=0}return e}}const D1=t=>{let e=t.toLowerCase().trim();const n={};if(t.indexOf("(")>-1){const r=t.split("(");e=r[0].toLowerCase().trim();const s=r[1].substring(0,r[1].length-1);e==="currency"&&s.indexOf(":")<0?n.currency||(n.currency=s.trim()):e==="relativetime"&&s.indexOf(":")<0?n.range||(n.range=s.trim()):s.split(";").forEach(a=>{if(a){const[o,...l]=a.split(":"),u=l.join(":").trim().replace(/^'+|'+$/g,""),h=o.trim();n[h]||(n[h]=u),u==="false"&&(n[h]=!1),u==="true"&&(n[h]=!0),isNaN(u)||(n[h]=parseInt(u,10))}})}return{formatName:e,formatOptions:n}},$n=t=>{const e={};return(n,r,s)=>{let i=s;s&&s.interpolationkey&&s.formatParams&&s.formatParams[s.interpolationkey]&&s[s.interpolationkey]&&(i={...i,[s.interpolationkey]:void 0});const a=r+JSON.stringify(i);let o=e[a];return o||(o=t(na(r),s),e[a]=o),o(n)}};class z1{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=vt.create("formatter"),this.options=e,this.formats={number:$n((n,r)=>{const s=new Intl.NumberFormat(n,{...r});return i=>s.format(i)}),currency:$n((n,r)=>{const s=new Intl.NumberFormat(n,{...r,style:"currency"});return i=>s.format(i)}),datetime:$n((n,r)=>{const s=new Intl.DateTimeFormat(n,{...r});return i=>s.format(i)}),relativetime:$n((n,r)=>{const s=new Intl.RelativeTimeFormat(n,{...r});return i=>s.format(i,r.range||"day")}),list:$n((n,r)=>{const s=new Intl.ListFormat(n,{...r});return i=>s.format(i)})},this.init(e)}init(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}};this.formatSeparator=n.interpolation.formatSeparator||","}add(e,n){this.formats[e.toLowerCase().trim()]=n}addCached(e,n){this.formats[e.toLowerCase().trim()]=$n(n)}format(e,n,r){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};const i=n.split(this.formatSeparator);if(i.length>1&&i[0].indexOf("(")>1&&i[0].indexOf(")")<0&&i.find(o=>o.indexOf(")")>-1)){const o=i.findIndex(l=>l.indexOf(")")>-1);i[0]=[i[0],...i.splice(1,o)].join(this.formatSeparator)}return i.reduce((o,l)=>{const{formatName:u,formatOptions:h}=D1(l);if(this.formats[u]){let d=o;try{const c=s&&s.formatParams&&s.formatParams[s.interpolationkey]||{},g=c.locale||c.lng||s.locale||s.lng||r;d=this.formats[u](o,g,{...h,...s,...c})}catch(c){this.logger.warn(c)}return d}else this.logger.warn(`there was no format function for ${u}`);return o},e)}}const U1=(t,e)=>{t.pending[e]!==void 0&&(delete t.pending[e],t.pendingCount--)};class B1 extends $a{constructor(e,n,r){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};super(),this.backend=e,this.store=n,this.services=r,this.languageUtils=r.languageUtils,this.options=s,this.logger=vt.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=s.maxParallelReads||10,this.readingCalls=0,this.maxRetries=s.maxRetries>=0?s.maxRetries:5,this.retryTimeout=s.retryTimeout>=1?s.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(r,s.backend,s)}queueLoad(e,n,r,s){const i={},a={},o={},l={};return e.forEach(u=>{let h=!0;n.forEach(d=>{const c=`${u}|${d}`;!r.reload&&this.store.hasResourceBundle(u,d)?this.state[c]=2:this.state[c]<0||(this.state[c]===1?a[c]===void 0&&(a[c]=!0):(this.state[c]=1,h=!1,a[c]===void 0&&(a[c]=!0),i[c]===void 0&&(i[c]=!0),l[d]===void 0&&(l[d]=!0)))}),h||(o[u]=!0)}),(Object.keys(i).length||Object.keys(a).length)&&this.queue.push({pending:a,pendingCount:Object.keys(a).length,loaded:{},errors:[],callback:s}),{toLoad:Object.keys(i),pending:Object.keys(a),toLoadLanguages:Object.keys(o),toLoadNamespaces:Object.keys(l)}}loaded(e,n,r){const s=e.split("|"),i=s[0],a=s[1];n&&this.emit("failedLoading",i,a,n),!n&&r&&this.store.addResourceBundle(i,a,r,void 0,void 0,{skipCopy:!0}),this.state[e]=n?-1:2,n&&r&&(this.state[e]=0);const o={};this.queue.forEach(l=>{k1(l.loaded,[i],a),U1(l,e),n&&l.errors.push(n),l.pendingCount===0&&!l.done&&(Object.keys(l.loaded).forEach(u=>{o[u]||(o[u]={});const h=l.loaded[u];h.length&&h.forEach(d=>{o[u][d]===void 0&&(o[u][d]=!0)})}),l.done=!0,l.errors.length?l.callback(l.errors):l.callback())}),this.emit("loaded",o),this.queue=this.queue.filter(l=>!l.done)}read(e,n,r){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:this.retryTimeout,a=arguments.length>5?arguments[5]:void 0;if(!e.length)return a(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:n,fcName:r,tried:s,wait:i,callback:a});return}this.readingCalls++;const o=(u,h)=>{if(this.readingCalls--,this.waitingReads.length>0){const d=this.waitingReads.shift();this.read(d.lng,d.ns,d.fcName,d.tried,d.wait,d.callback)}if(u&&h&&s<this.maxRetries){setTimeout(()=>{this.read.call(this,e,n,r,s+1,i*2,a)},i);return}a(u,h)},l=this.backend[r].bind(this.backend);if(l.length===2){try{const u=l(e,n);u&&typeof u.then=="function"?u.then(h=>o(null,h)).catch(o):o(null,u)}catch(u){o(u)}return}return l(e,n,o)}prepareLoading(e,n){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},s=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),s&&s();$(e)&&(e=this.languageUtils.toResolveHierarchy(e)),$(n)&&(n=[n]);const i=this.queueLoad(e,n,r,s);if(!i.toLoad.length)return i.pending.length||s(),null;i.toLoad.forEach(a=>{this.loadOne(a)})}load(e,n,r){this.prepareLoading(e,n,{},r)}reload(e,n,r){this.prepareLoading(e,n,{reload:!0},r)}loadOne(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";const r=e.split("|"),s=r[0],i=r[1];this.read(s,i,"read",void 0,void 0,(a,o)=>{a&&this.logger.warn(`${n}loading namespace ${i} for language ${s} failed`,a),!a&&o&&this.logger.log(`${n}loaded namespace ${i} for language ${s}`,o),this.loaded(e,a,o)})}saveMissing(e,n,r,s,i){let a=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{},o=arguments.length>6&&arguments[6]!==void 0?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(n)){this.logger.warn(`did not save key "${r}" as the namespace "${n}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(r==null||r==="")){if(this.backend&&this.backend.create){const l={...a,isUpdate:i},u=this.backend.create.bind(this.backend);if(u.length<6)try{let h;u.length===5?h=u(e,n,r,s,l):h=u(e,n,r,s),h&&typeof h.then=="function"?h.then(d=>o(null,d)).catch(o):o(null,h)}catch(h){o(h)}else u(e,n,r,s,o,l)}!e||!e[0]||this.store.addResource(e[0],n,r,s)}}}const td=()=>({debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:t=>{let e={};if(typeof t[1]=="object"&&(e=t[1]),$(t[1])&&(e.defaultValue=t[1]),$(t[2])&&(e.tDescription=t[2]),typeof t[2]=="object"||typeof t[3]=="object"){const n=t[3]||t[2];Object.keys(n).forEach(r=>{e[r]=n[r]})}return e},interpolation:{escapeValue:!0,format:t=>t,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),nd=t=>($(t.ns)&&(t.ns=[t.ns]),$(t.fallbackLng)&&(t.fallbackLng=[t.fallbackLng]),$(t.fallbackNS)&&(t.fallbackNS=[t.fallbackNS]),t.supportedLngs&&t.supportedLngs.indexOf("cimode")<0&&(t.supportedLngs=t.supportedLngs.concat(["cimode"])),t),li=()=>{},V1=t=>{Object.getOwnPropertyNames(Object.getPrototypeOf(t)).forEach(n=>{typeof t[n]=="function"&&(t[n]=t[n].bind(t))})};class vs extends $a{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;if(super(),this.options=nd(e),this.services={},this.logger=vt,this.modules={external:[]},V1(this),n&&!this.isInitialized&&!e.isClone){if(!this.options.initImmediate)return this.init(e,n),this;setTimeout(()=>{this.init(e,n)},0)}}init(){var e=this;let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,typeof n=="function"&&(r=n,n={}),!n.defaultNS&&n.defaultNS!==!1&&n.ns&&($(n.ns)?n.defaultNS=n.ns:n.ns.indexOf("translation")<0&&(n.defaultNS=n.ns[0]));const s=td();this.options={...s,...this.options,...nd(n)},this.options.compatibilityAPI!=="v1"&&(this.options.interpolation={...s.interpolation,...this.options.interpolation}),n.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=n.keySeparator),n.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=n.nsSeparator);const i=h=>h?typeof h=="function"?new h:h:null;if(!this.options.isClone){this.modules.logger?vt.init(i(this.modules.logger),this.options):vt.init(null,this.options);let h;this.modules.formatter?h=this.modules.formatter:typeof Intl<"u"&&(h=z1);const d=new qf(this.options);this.store=new Gf(this.options.resources,this.options);const c=this.services;c.logger=vt,c.resourceStore=this.store,c.languageUtils=d,c.pluralResolver=new M1(d,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),h&&(!this.options.interpolation.format||this.options.interpolation.format===s.interpolation.format)&&(c.formatter=i(h),c.formatter.init(c,this.options),this.options.interpolation.format=c.formatter.format.bind(c.formatter)),c.interpolator=new j1(this.options),c.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},c.backendConnector=new B1(i(this.modules.backend),c.resourceStore,c,this.options),c.backendConnector.on("*",function(g){for(var v=arguments.length,w=new Array(v>1?v-1:0),_=1;_<v;_++)w[_-1]=arguments[_];e.emit(g,...w)}),this.modules.languageDetector&&(c.languageDetector=i(this.modules.languageDetector),c.languageDetector.init&&c.languageDetector.init(c,this.options.detection,this.options)),this.modules.i18nFormat&&(c.i18nFormat=i(this.modules.i18nFormat),c.i18nFormat.init&&c.i18nFormat.init(this)),this.translator=new sa(this.services,this.options),this.translator.on("*",function(g){for(var v=arguments.length,w=new Array(v>1?v-1:0),_=1;_<v;_++)w[_-1]=arguments[_];e.emit(g,...w)}),this.modules.external.forEach(g=>{g.init&&g.init(this)})}if(this.format=this.options.interpolation.format,r||(r=li),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const h=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);h.length>0&&h[0]!=="dev"&&(this.options.lng=h[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(h=>{this[h]=function(){return e.store[h](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(h=>{this[h]=function(){return e.store[h](...arguments),e}});const l=Nr(),u=()=>{const h=(d,c)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),l.resolve(c),r(d,c)};if(this.languages&&this.options.compatibilityAPI!=="v1"&&!this.isInitialized)return h(null,this.t.bind(this));this.changeLanguage(this.options.lng,h)};return this.options.resources||!this.options.initImmediate?u():setTimeout(u,0),l}loadResources(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:li;const s=$(e)?e:this.language;if(typeof e=="function"&&(r=e),!this.options.resources||this.options.partialBundledLanguages){if(s&&s.toLowerCase()==="cimode"&&(!this.options.preload||this.options.preload.length===0))return r();const i=[],a=o=>{if(!o||o==="cimode")return;this.services.languageUtils.toResolveHierarchy(o).forEach(u=>{u!=="cimode"&&i.indexOf(u)<0&&i.push(u)})};s?a(s):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(l=>a(l)),this.options.preload&&this.options.preload.forEach(o=>a(o)),this.services.backendConnector.load(i,this.options.ns,o=>{!o&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),r(o)})}else r(null)}reloadResources(e,n,r){const s=Nr();return typeof e=="function"&&(r=e,e=void 0),typeof n=="function"&&(r=n,n=void 0),e||(e=this.languages),n||(n=this.options.ns),r||(r=li),this.services.backendConnector.reload(e,n,i=>{s.resolve(),r(i)}),s}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return e.type==="backend"&&(this.modules.backend=e),(e.type==="logger"||e.log&&e.warn&&e.error)&&(this.modules.logger=e),e.type==="languageDetector"&&(this.modules.languageDetector=e),e.type==="i18nFormat"&&(this.modules.i18nFormat=e),e.type==="postProcessor"&&Hm.addPostProcessor(e),e.type==="formatter"&&(this.modules.formatter=e),e.type==="3rdParty"&&this.modules.external.push(e),this}setResolvedLanguage(e){if(!(!e||!this.languages)&&!(["cimode","dev"].indexOf(e)>-1))for(let n=0;n<this.languages.length;n++){const r=this.languages[n];if(!(["cimode","dev"].indexOf(r)>-1)&&this.store.hasLanguageSomeTranslations(r)){this.resolvedLanguage=r;break}}}changeLanguage(e,n){var r=this;this.isLanguageChangingTo=e;const s=Nr();this.emit("languageChanging",e);const i=l=>{this.language=l,this.languages=this.services.languageUtils.toResolveHierarchy(l),this.resolvedLanguage=void 0,this.setResolvedLanguage(l)},a=(l,u)=>{u?(i(u),this.translator.changeLanguage(u),this.isLanguageChangingTo=void 0,this.emit("languageChanged",u),this.logger.log("languageChanged",u)):this.isLanguageChangingTo=void 0,s.resolve(function(){return r.t(...arguments)}),n&&n(l,function(){return r.t(...arguments)})},o=l=>{!e&&!l&&this.services.languageDetector&&(l=[]);const u=$(l)?l:this.services.languageUtils.getBestMatchFromCodes(l);u&&(this.language||i(u),this.translator.language||this.translator.changeLanguage(u),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(u)),this.loadResources(u,h=>{a(h,u)})};return!e&&this.services.languageDetector&&!this.services.languageDetector.async?o(this.services.languageDetector.detect()):!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(o):this.services.languageDetector.detect(o):o(e),s}getFixedT(e,n,r){var s=this;const i=function(a,o){let l;if(typeof o!="object"){for(var u=arguments.length,h=new Array(u>2?u-2:0),d=2;d<u;d++)h[d-2]=arguments[d];l=s.options.overloadTranslationOptionHandler([a,o].concat(h))}else l={...o};l.lng=l.lng||i.lng,l.lngs=l.lngs||i.lngs,l.ns=l.ns||i.ns,l.keyPrefix!==""&&(l.keyPrefix=l.keyPrefix||r||i.keyPrefix);const c=s.options.keySeparator||".";let g;return l.keyPrefix&&Array.isArray(a)?g=a.map(v=>`${l.keyPrefix}${c}${v}`):g=l.keyPrefix?`${l.keyPrefix}${c}${a}`:a,s.t(g,l)};return $(e)?i.lng=e:i.lngs=e,i.ns=n,i.keyPrefix=r,i}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const r=n.lng||this.resolvedLanguage||this.languages[0],s=this.options?this.options.fallbackLng:!1,i=this.languages[this.languages.length-1];if(r.toLowerCase()==="cimode")return!0;const a=(o,l)=>{const u=this.services.backendConnector.state[`${o}|${l}`];return u===-1||u===0||u===2};if(n.precheck){const o=n.precheck(this,a);if(o!==void 0)return o}return!!(this.hasResourceBundle(r,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||a(r,e)&&(!s||a(i,e)))}loadNamespaces(e,n){const r=Nr();return this.options.ns?($(e)&&(e=[e]),e.forEach(s=>{this.options.ns.indexOf(s)<0&&this.options.ns.push(s)}),this.loadResources(s=>{r.resolve(),n&&n(s)}),r):(n&&n(),Promise.resolve())}loadLanguages(e,n){const r=Nr();$(e)&&(e=[e]);const s=this.options.preload||[],i=e.filter(a=>s.indexOf(a)<0&&this.services.languageUtils.isSupportedCode(a));return i.length?(this.options.preload=s.concat(i),this.loadResources(a=>{r.resolve(),n&&n(a)}),r):(n&&n(),Promise.resolve())}dir(e){if(e||(e=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),!e)return"rtl";const n=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],r=this.services&&this.services.languageUtils||new qf(td());return n.indexOf(r.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;return new vs(e,n)}cloneInstance(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:li;const r=e.forkResourceStore;r&&delete e.forkResourceStore;const s={...this.options,...e,isClone:!0},i=new vs(s);return(e.debug!==void 0||e.prefix!==void 0)&&(i.logger=i.logger.clone(e)),["store","services","language"].forEach(o=>{i[o]=this[o]}),i.services={...this.services},i.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},r&&(i.store=new Gf(this.store.data,s),i.services.resourceStore=i.store),i.translator=new sa(i.services,s),i.translator.on("*",function(o){for(var l=arguments.length,u=new Array(l>1?l-1:0),h=1;h<l;h++)u[h-1]=arguments[h];i.emit(o,...u)}),i.init(s,n),i.translator.options=s,i.translator.backendConnector.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},i}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const ke=vs.createInstance();ke.createInstance=vs.createInstance;ke.createInstance;ke.dir;ke.init;ke.loadResources;ke.reloadResources;ke.use;ke.changeLanguage;ke.getFixedT;ke.t;ke.exists;ke.setDefaultNamespace;ke.hasLoadedNamespace;ke.loadNamespaces;ke.loadLanguages;function H1(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function ws(t){"@babel/helpers - typeof";return ws=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ws(t)}function W1(t,e){if(ws(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(ws(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}function K1(t){var e=W1(t,"string");return ws(e)=="symbol"?e:e+""}function Q1(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,K1(r.key),r)}}function J1(t,e,n){return e&&Q1(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}var Wm=[],X1=Wm.forEach,G1=Wm.slice;function Y1(t){return X1.call(G1.call(arguments,1),function(e){if(e)for(var n in e)t[n]===void 0&&(t[n]=e[n])}),t}var rd=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,q1=function(e,n,r){var s=r||{};s.path=s.path||"/";var i=encodeURIComponent(n),a="".concat(e,"=").concat(i);if(s.maxAge>0){var o=s.maxAge-0;if(Number.isNaN(o))throw new Error("maxAge should be a Number");a+="; Max-Age=".concat(Math.floor(o))}if(s.domain){if(!rd.test(s.domain))throw new TypeError("option domain is invalid");a+="; Domain=".concat(s.domain)}if(s.path){if(!rd.test(s.path))throw new TypeError("option path is invalid");a+="; Path=".concat(s.path)}if(s.expires){if(typeof s.expires.toUTCString!="function")throw new TypeError("option expires is invalid");a+="; Expires=".concat(s.expires.toUTCString())}if(s.httpOnly&&(a+="; HttpOnly"),s.secure&&(a+="; Secure"),s.sameSite){var l=typeof s.sameSite=="string"?s.sameSite.toLowerCase():s.sameSite;switch(l){case!0:a+="; SameSite=Strict";break;case"lax":a+="; SameSite=Lax";break;case"strict":a+="; SameSite=Strict";break;case"none":a+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}}return a},sd={create:function(e,n,r,s){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};r&&(i.expires=new Date,i.expires.setTime(i.expires.getTime()+r*60*1e3)),s&&(i.domain=s),document.cookie=q1(e,encodeURIComponent(n),i)},read:function(e){for(var n="".concat(e,"="),r=document.cookie.split(";"),s=0;s<r.length;s++){for(var i=r[s];i.charAt(0)===" ";)i=i.substring(1,i.length);if(i.indexOf(n)===0)return i.substring(n.length,i.length)}return null},remove:function(e){this.create(e,"",-1)}},Z1={name:"cookie",lookup:function(e){var n;if(e.lookupCookie&&typeof document<"u"){var r=sd.read(e.lookupCookie);r&&(n=r)}return n},cacheUserLanguage:function(e,n){n.lookupCookie&&typeof document<"u"&&sd.create(n.lookupCookie,e,n.cookieMinutes,n.cookieDomain,n.cookieOptions)}},ew={name:"querystring",lookup:function(e){var n;if(typeof window<"u"){var r=window.location.search;!window.location.search&&window.location.hash&&window.location.hash.indexOf("?")>-1&&(r=window.location.hash.substring(window.location.hash.indexOf("?")));for(var s=r.substring(1),i=s.split("&"),a=0;a<i.length;a++){var o=i[a].indexOf("=");if(o>0){var l=i[a].substring(0,o);l===e.lookupQuerystring&&(n=i[a].substring(o+1))}}}return n}},Lr=null,id=function(){if(Lr!==null)return Lr;try{Lr=window!=="undefined"&&window.localStorage!==null;var e="i18next.translate.boo";window.localStorage.setItem(e,"foo"),window.localStorage.removeItem(e)}catch{Lr=!1}return Lr},tw={name:"localStorage",lookup:function(e){var n;if(e.lookupLocalStorage&&id()){var r=window.localStorage.getItem(e.lookupLocalStorage);r&&(n=r)}return n},cacheUserLanguage:function(e,n){n.lookupLocalStorage&&id()&&window.localStorage.setItem(n.lookupLocalStorage,e)}},Or=null,ad=function(){if(Or!==null)return Or;try{Or=window!=="undefined"&&window.sessionStorage!==null;var e="i18next.translate.boo";window.sessionStorage.setItem(e,"foo"),window.sessionStorage.removeItem(e)}catch{Or=!1}return Or},nw={name:"sessionStorage",lookup:function(e){var n;if(e.lookupSessionStorage&&ad()){var r=window.sessionStorage.getItem(e.lookupSessionStorage);r&&(n=r)}return n},cacheUserLanguage:function(e,n){n.lookupSessionStorage&&ad()&&window.sessionStorage.setItem(n.lookupSessionStorage,e)}},rw={name:"navigator",lookup:function(e){var n=[];if(typeof navigator<"u"){if(navigator.languages)for(var r=0;r<navigator.languages.length;r++)n.push(navigator.languages[r]);navigator.userLanguage&&n.push(navigator.userLanguage),navigator.language&&n.push(navigator.language)}return n.length>0?n:void 0}},sw={name:"htmlTag",lookup:function(e){var n,r=e.htmlTag||(typeof document<"u"?document.documentElement:null);return r&&typeof r.getAttribute=="function"&&(n=r.getAttribute("lang")),n}},iw={name:"path",lookup:function(e){var n;if(typeof window<"u"){var r=window.location.pathname.match(/\/([a-zA-Z-]*)/g);if(r instanceof Array)if(typeof e.lookupFromPathIndex=="number"){if(typeof r[e.lookupFromPathIndex]!="string")return;n=r[e.lookupFromPathIndex].replace("/","")}else n=r[0].replace("/","")}return n}},aw={name:"subdomain",lookup:function(e){var n=typeof e.lookupFromSubdomainIndex=="number"?e.lookupFromSubdomainIndex+1:1,r=typeof window<"u"&&window.location&&window.location.hostname&&window.location.hostname.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i);if(r)return r[n]}},Km=!1;try{document.cookie,Km=!0}catch{}var Qm=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];Km||Qm.splice(1,1);function ow(){return{order:Qm,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:function(e){return e}}}var Jm=function(){function t(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};H1(this,t),this.type="languageDetector",this.detectors={},this.init(e,n)}return J1(t,[{key:"init",value:function(n){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=n||{languageUtils:{}},this.options=Y1(r,this.options||{},ow()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=function(i){return i.replace("-","_")}),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=s,this.addDetector(Z1),this.addDetector(ew),this.addDetector(tw),this.addDetector(nw),this.addDetector(rw),this.addDetector(sw),this.addDetector(iw),this.addDetector(aw)}},{key:"addDetector",value:function(n){return this.detectors[n.name]=n,this}},{key:"detect",value:function(n){var r=this;n||(n=this.options.order);var s=[];return n.forEach(function(i){if(r.detectors[i]){var a=r.detectors[i].lookup(r.options);a&&typeof a=="string"&&(a=[a]),a&&(s=s.concat(a))}}),s=s.map(function(i){return r.options.convertDetectedLanguage(i)}),this.services.languageUtils.getBestMatchFromCodes?s:s.length>0?s[0]:null}},{key:"cacheUserLanguage",value:function(n,r){var s=this;r||(r=this.options.caches),r&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(n)>-1||r.forEach(function(i){s.detectors[i]&&s.detectors[i].cacheUserLanguage(n,s.options)}))}}])}();Jm.type="languageDetector";const lw={title:"Detective Mystery",newCase:"New Case",loading:{title:"Finding Your Mystery",subtitle:"Gathering evidence and suspect information..."},background:"Background:",objective:"Objective:",suspectsWitnesses:"Suspects & Witnesses",evidenceClues:"Evidence & Clues",askQuestion:"Ask questions or share your theory...",footer:{rights:"© {{year}} Detective Mystery. All rights reserved.",privacy:"Privacy Policy",terms:"Terms of Use",contact:"Contact"},welcome:`Welcome to the case: "{{title}}". I'll be your assistant in solving this mystery. Feel free to ask questions about the case, the suspects, or share your theories.`,error:{aiResponse:"I apologize, but I'm having trouble processing your request at the moment. Please try again."}},uw={title:"侦探推理",newCase:"新案件",loading:{title:"寻找神秘案件",subtitle:"正在收集证据和嫌疑人信息..."},background:"案件背景：",objective:"目标：",suspectsWitnesses:"嫌疑人和证人",evidenceClues:"证据和线索",askQuestion:"提出问题或分享你的推理...",footer:{rights:"© {{year}} 侦探推理。保留所有权利。",privacy:"隐私政策",terms:"使用条款",contact:"联系我们"},welcome:'欢迎来到案件："{{title}}"。我是你的助手，将协助你解决这个谜案。你可以随时询问关于案件、嫌疑人的问题，或分享你的推理。',error:{aiResponse:"抱歉，我现在无法处理您的请求。请稍后再试。"}};ke.use(Jm).use(Xv).init({resources:{en:{translation:lw},zh:{translation:uw}},fallbackLng:"en",interpolation:{escapeValue:!1}});wp(document.getElementById("root")).render(C.jsx(V.StrictMode,{children:C.jsx(x1,{})}));
